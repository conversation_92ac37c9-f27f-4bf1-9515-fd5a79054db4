#!/usr/bin/env python3
"""
测试可用销售订单逻辑
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

from sqlalchemy import text
from app.core.database import get_db
from app.models.sales import SalesOrder, SalesOrderItem, SalesOutbound, SalesOutboundItem
from app.services.sales_service import SalesService

def test_available_orders():
    """测试可用销售订单逻辑"""
    
    db = next(get_db())
    service = SalesService(db)
    
    try:
        print("🧪 测试可用销售订单逻辑...")
        
        # 1. 查看所有已审核的销售订单
        print("\n1. 查看所有已审核的销售订单:")
        approved_orders = db.query(SalesOrder).filter(
            SalesOrder.status == 'approved'
        ).all()
        
        print(f"   找到 {len(approved_orders)} 个已审核的销售订单:")
        for order in approved_orders:
            print(f"   - {order.order_no}: {order.customer.name if order.customer else '未知客户'}")
            
            # 显示明细信息
            for item in order.items:
                shipped_qty = item.shipped_quantity or 0
                remaining_qty = item.quantity - shipped_qty
                print(f"     * {item.product_name}: 订单{item.quantity} - 已发{shipped_qty} = 剩余{remaining_qty}")
        
        # 2. 测试获取可用销售订单的方法
        print("\n2. 测试获取可用销售订单的方法:")
        available_orders = service.get_available_sales_orders()
        
        print(f"   找到 {len(available_orders)} 个可用的销售订单:")
        for order in available_orders:
            print(f"   - {order.order_no}: {order.customer_name}")
            print(f"     剩余明细数量: {len(order.remaining_items)}")
            
            total_remaining = 0
            for item in order.remaining_items:
                print(f"     * 行{item['line_number']}: {item['product_name']} - 剩余{item['quantity']}件")
                total_remaining += item['quantity']
            
            print(f"     总剩余数量: {total_remaining}件")
        
        # 3. 验证过滤逻辑
        print("\n3. 验证过滤逻辑:")
        
        # 查找一个已完全发货的订单
        fully_shipped_orders = []
        for order in approved_orders:
            all_shipped = True
            for item in order.items:
                shipped_qty = item.shipped_quantity or 0
                if shipped_qty < item.quantity:
                    all_shipped = False
                    break
            
            if all_shipped and order.items:
                fully_shipped_orders.append(order)
        
        print(f"   已完全发货的订单数量: {len(fully_shipped_orders)}")
        for order in fully_shipped_orders:
            print(f"   - {order.order_no}: 已完全发货")
        
        # 验证这些订单不在可用订单列表中
        available_order_ids = [order.id for order in available_orders]
        fully_shipped_in_available = [order for order in fully_shipped_orders if order.id in available_order_ids]
        
        if fully_shipped_in_available:
            print(f"   ❌ 发现 {len(fully_shipped_in_available)} 个已完全发货的订单仍在可用列表中")
        else:
            print(f"   ✅ 已完全发货的订单正确被过滤掉")
        
        # 4. 测试客户过滤
        if available_orders:
            test_customer_id = available_orders[0].customer_id
            print(f"\n4. 测试客户过滤 (客户ID: {test_customer_id}):")
            
            filtered_orders = service.get_available_sales_orders(test_customer_id)
            print(f"   过滤后的订单数量: {len(filtered_orders)}")
            
            for order in filtered_orders:
                if order.customer_id != test_customer_id:
                    print(f"   ❌ 发现错误的客户订单: {order.order_no}")
                else:
                    print(f"   ✅ 客户过滤正确: {order.order_no}")
        
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_available_orders()
