#!/usr/bin/env python3
"""
调试服务层代码
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

from app.core.database import get_db
from app.services.sales_service import SalesService

def debug_service():
    """调试服务层代码"""
    
    db = next(get_db())
    service = SalesService(db)
    
    try:
        print("🔍 调试服务层代码...")
        
        # 直接调用服务方法
        print("\n📞 调用 get_available_sales_orders 方法...")
        orders = service.get_available_sales_orders()
        
        print(f"   ✅ 方法调用成功")
        print(f"   返回类型: {type(orders)}")
        print(f"   订单数量: {len(orders)}")
        
        if orders:
            first_order = orders[0]
            print(f"\n   第一个订单:")
            print(f"     类型: {type(first_order)}")
            print(f"     键: {list(first_order.keys()) if isinstance(first_order, dict) else 'Not a dict'}")
            
            if isinstance(first_order, dict):
                print(f"     ID: {first_order.get('id')}")
                print(f"     订单号: {first_order.get('order_no')}")
                print(f"     客户名: {first_order.get('customer_name')}")
                print(f"     剩余明细: {len(first_order.get('remaining_items', []))}")
        
        print("\n✅ 调试完成")
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    debug_service()
