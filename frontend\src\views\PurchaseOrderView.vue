<template>
  <div class="purchase-order-view">
    <div class="page-header">
      <div class="header-left">
        <h2>采购订单</h2>
        <p class="page-description">管理采购订单，跟踪采购进度和状态</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建采购订单
        </el-button>
        <el-button @click="exportOrders">
          <el-icon><Download /></el-icon>
          导出订单
        </el-button>
      </div>
    </div>

    <!-- 订单概览 -->
    <el-row :gutter="20" class="order-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon size="32"><Document /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ orderStats.total_orders }}</div>
              <div class="overview-label">订单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon pending">
              <el-icon size="32"><Clock /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ orderStats.pending_orders }}</div>
              <div class="overview-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon processing">
              <el-icon size="32"><Loading /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ orderStats.processing_orders }}</div>
              <div class="overview-label">处理中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon completed">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ orderStats.completed_orders }}</div>
              <div class="overview-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="订单编号">
          <el-input
            v-model="searchForm.order_no"
            placeholder="请输入订单编号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="供应商">
          <el-select v-model="searchForm.supplier_id" placeholder="选择供应商" clearable style="width: 200px">
            <el-option
              v-for="supplier in suppliers"
              :key="supplier.id"
              :label="supplier.name"
              :value="supplier.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="订单状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="草稿" value="draft" />
            <el-option label="已提交" value="submitted" />
            <el-option label="已审批" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="采购中" value="purchasing" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY年MM月DD日"
            value-format="YYYY-MM-DD"
            style="width: 280px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="searchOrders">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 采购订单列表 -->
    <el-card class="order-list-card">
      <template #header>
        <span>采购订单列表 (共 {{ total }} 条)</span>
        <div class="header-actions">
          <el-button-group>
            <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
              <el-icon><List /></el-icon>
              列表视图
            </el-button>
            <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
              <el-icon><Grid /></el-icon>
              卡片视图
            </el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="purchaseOrders" style="width: 100%" v-loading="loading">
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="order_no" label="订单编号" width="150">
            <template #default="{ row }">
              <el-link type="primary" @click="viewOrderDetail(row)">
                {{ row.order_no }}
              </el-link>
            </template>
          </el-table-column>
          
          <el-table-column prop="supplier_name" label="供应商" width="200" />
          
          <el-table-column prop="total_amount" label="订单金额" width="120">
            <template #default="{ row }">
              <span class="amount">¥{{ row.total_amount.toFixed(2) }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="item_count" label="商品数量" width="100">
            <template #default="{ row }">
              <span>{{ row.items?.length || 0 }}种</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="订单状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="expected_date" label="预计到货" width="120">
            <template #default="{ row }">
              {{ formatDate(row.expected_date) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="created_by" label="创建人" width="100" />

          <el-table-column prop="submitted_by" label="提交人" width="100">
            <template #default="{ row }">
              {{ row.submitted_by || '-' }}
            </template>
          </el-table-column>

          <el-table-column prop="approved_by" label="审核人" width="100">
            <template #default="{ row }">
              {{ row.approved_by || '-' }}
            </template>
          </el-table-column>

          <el-table-column prop="created_at" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="220" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button size="small" @click="viewOrderDetail(row)">查看</el-button>

                <!-- 草稿状态：显示提交按钮 -->
                <template v-if="row.status === 'draft'">
                  <el-button size="small" type="success" @click="submitOrder(row)">提交</el-button>
                </template>

                <!-- 已提交状态：显示撤回按钮 -->
                <template v-if="row.status === 'submitted'">
                  <el-button size="small" type="warning" @click="recallOrder(row)">撤回</el-button>
                </template>

                <!-- 已审核状态：显示开始采购按钮 -->
                <template v-if="row.status === 'approved'">
                  <el-button size="small" type="primary" @click="startPurchasing(row)">开始采购</el-button>
                </template>

                <!-- 更多操作菜单 -->
                <el-dropdown @command="(command: string) => handleOrderAction(command, row)" v-if="row.status !== 'completed' && row.status !== 'cancelled' && row.status !== 'rejected'">
                  <el-button size="small">
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <!-- 草稿状态：编辑 -->
                      <el-dropdown-item command="edit" v-if="row.status === 'draft'">编辑</el-dropdown-item>

                      <!-- 已提交状态：审核选项 -->
                      <el-dropdown-item command="approve" v-if="row.status === 'submitted'">审核通过</el-dropdown-item>
                      <el-dropdown-item command="reject" v-if="row.status === 'submitted'">审核拒绝</el-dropdown-item>

                      <!-- 分隔线 -->
                      <el-dropdown-item divided command="cancel">取消订单</el-dropdown-item>
                      <el-dropdown-item command="delete" v-if="row.status === 'draft'">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div class="orders-grid">
          <div 
            v-for="order in purchaseOrders" 
            :key="order.id"
            class="order-card"
          >
            <div class="card-header">
              <div class="order-info">
                <h4 class="order-no">{{ order.order_no }}</h4>
                <p class="supplier-name">{{ order.supplier_name }}</p>
              </div>
              <div class="order-status">
                <el-tag :type="getStatusTagType(order.status)" size="small">
                  {{ getStatusLabel(order.status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="card-content">
              <div class="order-details">
                <div class="detail-row">
                  <span class="label">订单金额:</span>
                  <span class="amount">¥{{ order.total_amount.toFixed(2) }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">商品数量:</span>
                  <span class="value">{{ order.items?.length || 0 }}种</span>
                </div>
                <div class="detail-row">
                  <span class="label">预计到货:</span>
                  <span class="value">{{ formatDate(order.expected_date) }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">创建人:</span>
                  <span class="value">{{ order.created_by }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">创建时间:</span>
                  <span class="value">{{ formatDate(order.created_at) }}</span>
                </div>
              </div>
              
              <div class="progress-section" v-if="order.status !== 'cancelled'">
                <div class="progress-label">订单进度</div>
                <el-progress 
                  :percentage="getOrderProgress(order.status)"
                  :color="getProgressColor(order.status)"
                  :show-text="false"
                />
              </div>
            </div>
            
            <div class="card-actions">
              <el-button size="small" @click="viewOrderDetail(order)">查看详情</el-button>

              <!-- 草稿状态：显示提交按钮 -->
              <template v-if="order.status === 'draft'">
                <el-button size="small" type="success" @click="submitOrder(order)">提交</el-button>
              </template>

              <!-- 已提交状态：显示撤回按钮 -->
              <template v-if="order.status === 'submitted'">
                <el-button size="small" type="warning" @click="recallOrder(order)">撤回</el-button>
              </template>

              <!-- 已审核状态：显示开始采购按钮 -->
              <template v-if="order.status === 'approved'">
                <el-button size="small" type="primary" @click="startPurchasing(order)">开始采购</el-button>
              </template>

              <!-- 更多操作菜单 -->
              <el-dropdown @command="(command: string) => handleOrderAction(command, order)" v-if="order.status !== 'completed' && order.status !== 'cancelled' && order.status !== 'rejected'">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- 草稿状态：编辑 -->
                    <el-dropdown-item command="edit" v-if="order.status === 'draft'">编辑</el-dropdown-item>

                    <!-- 已提交状态：审核选项 -->
                    <el-dropdown-item command="approve" v-if="order.status === 'submitted'">审核通过</el-dropdown-item>
                    <el-dropdown-item command="reject" v-if="order.status === 'submitted'">审核拒绝</el-dropdown-item>

                    <!-- 分隔线 -->
                    <el-dropdown-item divided command="cancel">取消订单</el-dropdown-item>
                    <el-dropdown-item command="delete" v-if="order.status === 'draft'">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新建/编辑采购订单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingOrder ? '编辑采购订单' : '新建采购订单'"
      width="900px"
      @close="resetOrderForm"
    >
      <el-form :model="orderForm" :rules="orderRules" ref="orderFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplier_id">
              <el-select v-model="orderForm.supplier_id" placeholder="选择供应商" style="width: 100%">
                <el-option
                  v-for="supplier in suppliers"
                  :key="supplier.id"
                  :label="supplier.name"
                  :value="supplier.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计到货日期" prop="expected_date">
              <el-date-picker
                v-model="orderForm.expected_date"
                type="date"
                placeholder="选择预计到货日期"
                format="YYYY年MM月DD日"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注信息">
          <el-input
            v-model="orderForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
        
        <!-- 采购商品列表 -->
        <el-form-item label="采购商品" required>
          <div class="purchase-items">
            <div class="items-header">
              <el-button size="small" @click="addPurchaseItem">
                <el-icon><Plus /></el-icon>
                添加商品
              </el-button>
            </div>
            
            <el-table :data="orderForm.items" style="width: 100%; margin-top: 10px;">
              <el-table-column label="商品名称" min-width="200">
                <template #default="{ row, $index }">
                  <el-select
                    v-model="row.product_id"
                    placeholder="选择商品"
                    filterable
                    @change="onProductChange(row, $index)"
                  >
                    <el-option
                      v-for="product in products"
                      :key="product.id"
                      :label="`${product.name} (${product.sku})`"
                      :value="product.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              
              <el-table-column label="采购数量" width="120">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.quantity"
                    :min="1"
                    @change="calculateItemTotal(row)"
                  />
                </template>
              </el-table-column>
              
              <el-table-column label="采购单价" width="120">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.unit_price"
                    :min="0"
                    :precision="2"
                    @change="calculateItemTotal(row)"
                  />
                </template>
              </el-table-column>
              
              <el-table-column label="小计" width="120">
                <template #default="{ row }">
                  <span class="amount">¥{{ (row.quantity * row.unit_price).toFixed(2) }}</span>
                </template>
              </el-table-column>
              
              <el-table-column label="操作" width="80">
                <template #default="{ $index }">
                  <el-button size="small" type="danger" @click="removePurchaseItem($index)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="total-section">
              <div class="total-row">
                <span class="total-label">订单总金额:</span>
                <span class="total-amount">¥{{ calculateTotalAmount().toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveOrder" :loading="saving">
          {{ editingOrder ? '更新' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="采购订单详情"
      width="1000px"
    >
      <div v-if="selectedOrder" class="order-detail">
        <div class="detail-header">
          <div class="order-basic-info">
            <h3>{{ selectedOrder.order_no }}</h3>
            <el-tag :type="getStatusTagType(selectedOrder.status)">
              {{ getStatusLabel(selectedOrder.status) }}
            </el-tag>
          </div>
          <div class="order-amount">
            <span class="amount-label">订单金额</span>
            <span class="amount-value">¥{{ selectedOrder.total_amount.toFixed(2) }}</span>
          </div>
        </div>
        
        <div class="detail-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="供应商">{{ selectedOrder.supplier_name }}</el-descriptions-item>
            <el-descriptions-item label="创建人">{{ selectedOrder.created_by }}</el-descriptions-item>
            <el-descriptions-item label="预计到货">{{ formatDate(selectedOrder.expected_date) }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDateTime(selectedOrder.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="备注信息" :span="2">{{ selectedOrder.remark || '无' }}</el-descriptions-item>
          </el-descriptions>
          
          <div class="items-section">
            <h4>采购商品明细</h4>
            <el-table
              :data="selectedOrder.items"
              style="width: 100%;"
              border
              stripe
            >
              <el-table-column prop="product_name" label="商品名称" min-width="200" show-overflow-tooltip />
              <el-table-column prop="product_sku" label="商品SKU" width="120" />
              <el-table-column prop="quantity" label="采购数量" width="90" align="center">
                <template #default="{ row }">
                  <span>{{ row.quantity || 0 }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="received_quantity" label="已收货" width="90" align="center">
                <template #default="{ row }">
                  <span>{{ row.received_quantity || 0 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="剩余数量" width="90" align="center">
                <template #default="{ row }">
                  <span class="remaining-quantity">{{ (row.quantity || 0) - (row.received_quantity || 0) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="unit_price" label="单价" width="120" align="right">
                <template #default="{ row }">
                  <span>¥{{ (row.unit_price || 0).toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="小计" width="130" align="right">
                <template #default="{ row }">
                  <span class="amount">¥{{ ((row.quantity || 0) * (row.unit_price || 0)).toFixed(2) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { purchaseApi, type PurchaseOrder, type PurchaseOrderStats, type PurchaseOrderCreate, type PurchaseOrderItem, PurchaseOrderStatus } from '@/api/purchase'
import { supplierApi, type Supplier } from '@/api/suppliers'
import { productApi, type Product } from '@/api/products'
import {
  Plus,
  Download,
  Document,
  Clock,
  Loading,
  CircleCheck,
  Search,
  List,
  Grid,
  ArrowDown
} from '@element-plus/icons-vue'

// 类型定义






interface SearchForm {
  order_no: string
  supplier_id: number | null
  status: string
  date_range: [string, string] | null
}

interface OrderForm {
  supplier_id: number | null
  expected_date: string | null
  remark: string
  items: {
    product_id: number
    line_number: number
    product_name?: string
    product_sku?: string
    quantity: number
    unit_price: number
    total_price: number
  }[]
}

// 响应式数据
const purchaseOrders = ref<PurchaseOrder[]>([])
const suppliers = ref<Supplier[]>([])
const products = ref<Product[]>([])
const loading = ref(false)
const saving = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')

const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const editingOrder = ref<PurchaseOrder | null>(null)
const selectedOrder = ref<PurchaseOrder | null>(null)

const orderStats = ref<PurchaseOrderStats>({
  total_orders: 0,
  pending_orders: 0,
  processing_orders: 0,
  completed_orders: 0,
  total_amount: 0,
  this_month_orders: 0
})

const searchForm = reactive<SearchForm>({
  order_no: '',
  supplier_id: null,
  status: '',
  date_range: null
})

const orderForm = reactive<OrderForm>({
  supplier_id: null,
  expected_date: null,
  remark: '',
  items: []
})

const orderFormRef = ref()

// 表单验证规则
const orderRules = {
  supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  expected_date: [{ required: true, message: '请选择预计到货日期', trigger: 'change' }]
}

// 方法
const fetchPurchaseOrders = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 添加搜索条件
    if (searchForm.order_no) {
      params.order_no = searchForm.order_no
    }
    if (searchForm.supplier_id) {
      params.supplier_id = searchForm.supplier_id
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.date_range && searchForm.date_range.length === 2) {
      params.start_date = searchForm.date_range[0]
      params.end_date = searchForm.date_range[1]
    }

    // 调用采购订单API
    const response = await purchaseApi.getPurchaseOrders(params) as any

    if (response) {
      purchaseOrders.value = response.items || []
      total.value = response.total || 0
    } else {
      purchaseOrders.value = []
      total.value = 0
    }

    // 获取统计数据
    const statsResponse = await purchaseApi.getPurchaseOrderStats() as any
    if (statsResponse) {
      orderStats.value = statsResponse
    }
  } catch (error) {
    ElMessage.error('获取采购订单失败')
  } finally {
    loading.value = false
  }
}

const fetchSuppliers = async () => {
  try {
    const response = await supplierApi.getSuppliers() as any
    if (response) {
      suppliers.value = response.items || []
    }
  } catch (error) {
    console.error('获取供应商失败:', error)
  }
}

const fetchProducts = async () => {
  try {
    const response = await productApi.getProducts() as any
    if (response) {
      products.value = response.items || []
    }
  } catch (error) {
    console.error('获取商品失败:', error)
  }
}

const searchOrders = () => {
  // 重置到第一页并执行搜索
  currentPage.value = 1
  fetchPurchaseOrders()
}

const resetSearch = () => {
  // 重置搜索表单
  Object.assign(searchForm, {
    order_no: '',
    supplier_id: null,
    status: '',
    date_range: null
  })
  // 重置到第一页并重新获取数据
  currentPage.value = 1
  fetchPurchaseOrders()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchPurchaseOrders()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchPurchaseOrders()
}

const viewOrderDetail = (order: PurchaseOrder) => {
  selectedOrder.value = order
  showDetailDialog.value = true
}

const editOrder = (order: PurchaseOrder) => {
  editingOrder.value = order
  Object.assign(orderForm, {
    supplier_id: order.supplier_id,
    expected_date: order.expected_date ? order.expected_date.split('T')[0] : null,
    remark: order.remark || '',
    items: (order.items || []).map((item, index) => ({
      ...item,
      line_number: item.line_number || (index + 1) // 如果没有行号则自动生成
    }))
  })
  showCreateDialog.value = true
}

// 提交采购订单
const submitOrder = async (order: PurchaseOrder) => {
  try {
    await ElMessageBox.confirm(
      `确定要提交采购订单 ${order.order_no} 吗？提交后将无法编辑。`,
      '确认提交',
      {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await purchaseApi.submitPurchaseOrder(order.id)
    ElMessage.success('采购订单提交成功')
    await fetchPurchaseOrders()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('提交采购订单失败:', error)
      ElMessage.error(error.response?.data?.detail || '提交失败')
    }
  }
}

// 撤回采购订单
const recallOrder = async (order: PurchaseOrder) => {
  try {
    await ElMessageBox.confirm(
      `确定要撤回采购订单 ${order.order_no} 吗？撤回后可以重新编辑。`,
      '确认撤回',
      {
        confirmButtonText: '确定撤回',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await purchaseApi.recallPurchaseOrder(order.id)
    ElMessage.success('采购订单撤回成功')
    await fetchPurchaseOrders()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('撤回采购订单失败:', error)
      ElMessage.error(error.response?.data?.detail || '撤回失败')
    }
  }
}

// 审核采购订单
const approveOrder = async (order: PurchaseOrder, approved: boolean = true) => {
  try {
    const action = approved ? '通过' : '拒绝'
    await ElMessageBox.confirm(
      `确定要审核${action}采购订单 ${order.order_no} 吗？`,
      `确认审核${action}`,
      {
        confirmButtonText: `审核${action}`,
        cancelButtonText: '取消',
        type: approved ? 'warning' : 'error'
      }
    )

    await purchaseApi.approvePurchaseOrder(order.id, approved)
    ElMessage.success(`采购订单审核${action}成功`)
    await fetchPurchaseOrders()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('审核采购订单失败:', error)
      ElMessage.error(error.response?.data?.detail || '审核失败')
    }
  }
}

// 开始采购
const startPurchasing = async (order: PurchaseOrder) => {
  try {
    await ElMessageBox.confirm(
      `确定要开始采购订单 ${order.order_no} 吗？`,
      '确认开始采购',
      {
        confirmButtonText: '开始采购',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await purchaseApi.updateOrderStatus(order.id, PurchaseOrderStatus.PURCHASING)
    ElMessage.success('采购订单已开始执行')
    await fetchPurchaseOrders()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('开始采购失败:', error)
      ElMessage.error(error.response?.data?.detail || '操作失败')
    }
  }
}

const handleOrderAction = async (command: string, order: PurchaseOrder) => {
  try {
    switch (command) {
      case 'edit':
        editOrder(order)
        return

      case 'approve':
        await approveOrder(order, true)
        return

      case 'reject':
        await approveOrder(order, false)
        return

      case 'cancel':
        await ElMessageBox.confirm(
          '确定要取消这个采购订单吗？',
          '确认取消',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await purchaseApi.updateOrderStatus(order.id, PurchaseOrderStatus.CANCELLED, '当前用户')
        ElMessage.success('采购订单取消成功')
        break

      case 'delete':
        await ElMessageBox.confirm(
          `确定要删除采购订单 ${order.order_no} 吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'error'
          }
        )
        await purchaseApi.deletePurchaseOrder(order.id)
        ElMessage.success('采购订单删除成功')
        break
    }

    // 重新获取数据
    await fetchPurchaseOrders()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('操作失败:', error)
      ElMessage.error(error.response?.data?.detail || '操作失败')
    }
  }
}

const addPurchaseItem = () => {
  orderForm.items.push({
    product_id: 0,
    line_number: orderForm.items.length + 1,
    product_name: '',
    product_sku: '',
    quantity: 1,
    unit_price: 0,
    total_price: 0
  })
}

const removePurchaseItem = (index: number) => {
  orderForm.items.splice(index, 1)
}

const onProductChange = (item: any, index: number) => {
  const product = products.value.find(p => p.id === item.product_id)
  if (product) {
    item.product_name = product.name
    item.product_sku = product.sku
    item.unit_price = product.price
    item.total_price = item.quantity * product.price
  }
}

const calculateItemTotal = (item: any) => {
  item.total_price = item.quantity * item.unit_price
}

const calculateTotalAmount = (): number => {
  return orderForm.items.reduce((total: number, item: any) => {
    return total + (item.quantity * item.unit_price)
  }, 0)
}

const saveOrder = async () => {
  if (!orderFormRef.value) return

  try {
    await orderFormRef.value.validate()

    if (orderForm.items.length === 0) {
      ElMessage.warning('请至少添加一个采购商品')
      return
    }

    saving.value = true

    // 准备API数据
    const orderData: PurchaseOrderCreate = {
      supplier_id: orderForm.supplier_id!,
      total_amount: calculateTotalAmount(),
      status: PurchaseOrderStatus.DRAFT,
      expected_date: orderForm.expected_date ? new Date(orderForm.expected_date).toISOString() : undefined,
      // created_by 将由后端从当前用户会话中获取
      remark: orderForm.remark,
      items: orderForm.items.map(item => ({
        product_id: item.product_id,
        line_number: item.line_number,
        product_name: item.product_name || '',
        product_sku: item.product_sku,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price
      }))
    }

    if (editingOrder.value) {
      // 更新现有订单
      await purchaseApi.updatePurchaseOrder(editingOrder.value.id, orderData)
      ElMessage.success('订单更新成功')
    } else {
      // 新增订单
      await purchaseApi.createPurchaseOrder(orderData)
      ElMessage.success('订单创建成功')
    }

    // 重新获取数据
    await fetchPurchaseOrders()

    showCreateDialog.value = false
    resetOrderForm()
  } catch (error: any) {
    console.error('保存失败:', error)
    const errorMessage = error.response?.data?.detail || error.message || '保存失败'
    ElMessage.error(errorMessage)
  } finally {
    saving.value = false
  }
}

const resetOrderForm = () => {
  editingOrder.value = null
  Object.assign(orderForm, {
    supplier_id: null,
    expected_date: null,
    remark: '',
    items: []
  })
  if (orderFormRef.value) {
    orderFormRef.value.clearValidate()
  }
}

const exportOrders = () => {
  ElMessage.success('采购订单导出成功')
}

// 辅助方法
const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    'draft': '草稿',
    'submitted': '已提交',
    'approved': '已审批',
    'rejected': '已拒绝',
    'purchasing': '采购中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return labelMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'draft': 'info',
    'submitted': 'warning',
    'approved': 'primary',
    'rejected': 'danger',
    'purchasing': 'info',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

const getOrderProgress = (status: string) => {
  const progressMap: Record<string, number> = {
    'draft': 10,
    'submitted': 30,
    'approved': 50,
    'rejected': 0,
    'purchasing': 70,
    'completed': 100,
    'cancelled': 0
  }
  return progressMap[status] || 0
}

const getProgressColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'draft': '#909399',
    'submitted': '#E6A23C',
    'approved': '#409EFF',
    'rejected': '#F56C6C',
    'purchasing': '#909399',
    'completed': '#67C23A',
    'cancelled': '#F56C6C'
  }
  return colorMap[status] || '#909399'
}

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string | undefined) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchPurchaseOrders()
  fetchSuppliers()
  fetchProducts()
})
</script>

<style>
.purchase-order-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 订单概览 */
.order-overview {
  margin-bottom: 12px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.total {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.pending {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-icon.processing {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.overview-icon.completed {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 订单列表卡片 */
.order-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 调整采购订单列表卡片头部的内边距并设置布局 */
.order-list-card .el-card__header {
  padding: 14px 20px !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 金额样式 */
.amount {
  font-weight: 600;
  color: #E6A23C;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.order-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.order-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.supplier-name {
  margin: 0;
  font-size: 12px;
  opacity: 0.9;
}

.card-content {
  padding: 16px;
}

.order-details {
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-row .label {
  color: #606266;
}

.detail-row .value {
  color: #2c3e50;
}

.progress-section {
  margin-top: 16px;
}

.progress-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
}

.card-actions {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 订单详情 */
.order-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.order-basic-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 20px;
}

.order-amount {
  text-align: right;
}

.amount-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.amount-value {
  font-size: 24px;
  font-weight: 700;
  color: #E6A23C;
}

.detail-content {
  margin-top: 20px;
}

.items-section {
  margin-top: 20px;
}

.items-section h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 16px;
}

/* 采购商品表单 */
.purchase-items {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.items-header {
  margin-bottom: 16px;
}

.total-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.total-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}

.total-label {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.total-amount {
  font-size: 20px;
  font-weight: 700;
  color: #E6A23C;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

/* 按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 链接样式 */
.el-link {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .order-overview .el-col {
    margin-bottom: 16px;
  }

  .orders-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  .purchase-order-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
  }

  .orders-grid {
    grid-template-columns: 1fr;
  }

  .detail-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .order-amount {
    text-align: left;
  }
}

/* 表格操作按钮样式 */
.table-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: nowrap;

  .el-button {
    margin: 0;
    min-width: auto;
    padding: 5px 8px;
  }

  .el-dropdown {
    .el-button {
      padding: 5px 8px;
    }
  }
}

/* 剩余数量样式 */
.remaining-quantity {
  color: #409EFF;
  font-weight: 600;
}

/* 订单详情表格样式 */
.items-section .el-table {
  max-width: 100%;
  overflow-x: auto;
}
</style>
