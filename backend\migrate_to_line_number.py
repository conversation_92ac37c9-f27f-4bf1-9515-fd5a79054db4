#!/usr/bin/env python3
"""
数据库迁移脚本：将销售出库单明细表的sales_order_item_id字段改为sales_order_line_number字段
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

from sqlalchemy import text
from app.core.database import get_db

def migrate_to_line_number():
    """执行数据库迁移：从sales_order_item_id改为sales_order_line_number"""
    
    db = next(get_db())
    
    try:
        print("🔄 开始数据库迁移：将sales_order_item_id改为sales_order_line_number...")
        
        # 1. 检查当前表结构
        print("\n1. 检查当前表结构:")
        result = db.execute(text("SELECT * FROM sales_outbound_items LIMIT 1"))
        columns = list(result.keys())
        print(f"   当前字段: {columns}")
        
        has_item_id = 'sales_order_item_id' in columns
        has_line_number = 'sales_order_line_number' in columns
        
        print(f"   sales_order_item_id 存在: {has_item_id}")
        print(f"   sales_order_line_number 存在: {has_line_number}")
        
        # 2. 如果没有line_number字段，添加它
        if not has_line_number:
            print("\n2. 添加 sales_order_line_number 字段...")
            db.execute(text("""
                ALTER TABLE sales_outbound_items 
                ADD COLUMN sales_order_line_number INTEGER
            """))
            print("   ✅ sales_order_line_number 字段添加成功")
        else:
            print("\n2. ✅ sales_order_line_number 字段已存在")
        
        # 3. 如果有item_id字段，迁移数据
        if has_item_id:
            print("\n3. 迁移数据：从sales_order_item_id获取line_number...")
            
            # 更新sales_order_line_number字段，通过关联sales_order_items表获取line_number
            result = db.execute(text("""
                UPDATE sales_outbound_items soi
                SET sales_order_line_number = (
                    SELECT so_item.line_number
                    FROM sales_order_items so_item
                    WHERE so_item.id = soi.sales_order_item_id
                )
                WHERE soi.sales_order_item_id IS NOT NULL
            """))
            
            updated_rows = result.rowcount
            print(f"   📊 更新了 {updated_rows} 条记录")
            
            # 4. 删除旧的sales_order_item_id字段
            print("\n4. 删除旧的 sales_order_item_id 字段...")
            
            # 先删除外键约束（如果存在）
            try:
                db.execute(text("""
                    ALTER TABLE sales_outbound_items 
                    DROP FOREIGN KEY fk_sales_outbound_items_sales_order_item_id
                """))
                print("   ✅ 外键约束删除成功")
            except Exception as e:
                print(f"   ⚠️ 外键约束删除失败（可能不存在）: {e}")
            
            # 删除字段
            db.execute(text("""
                ALTER TABLE sales_outbound_items 
                DROP COLUMN sales_order_item_id
            """))
            print("   ✅ sales_order_item_id 字段删除成功")
        else:
            print("\n3. ✅ 没有sales_order_item_id字段，跳过数据迁移")
        
        # 5. 验证迁移结果
        print("\n5. 验证迁移结果...")
        result = db.execute(text("""
            SELECT 
                COUNT(*) as total_items,
                COUNT(sales_order_line_number) as with_line_number
            FROM sales_outbound_items
        """))
        
        stats = result.fetchone()
        print(f"   总出库单明细: {stats.total_items}")
        print(f"   有行号的明细: {stats.with_line_number}")
        
        # 检查最终表结构
        result = db.execute(text("SELECT * FROM sales_outbound_items LIMIT 1"))
        final_columns = list(result.keys())
        print(f"   最终字段: {final_columns}")
        
        # 提交事务
        db.commit()
        print("\n✅ 数据库迁移完成！")
        
    except Exception as e:
        print(f"❌ 迁移失败: {str(e)}")
        db.rollback()
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    migrate_to_line_number()
