<template>
  <div class="purchase-receipt-view">
    <div class="page-header">
      <div class="header-left">
        <h2>采购入库单</h2>
        <p class="page-description">管理采购入库单，跟踪商品入库情况</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建入库单
        </el-button>

        <el-button @click="exportReceipts">
          <el-icon><Download /></el-icon>
          导出入库单
        </el-button>
      </div>
    </div>

    <!-- 入库单概览 -->
    <el-row :gutter="20" class="receipt-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon size="32"><Tickets /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ receiptStats?.total_receipts || 0 }}</div>
              <div class="overview-label">入库单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon pending">
              <el-icon size="32"><Clock /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ receiptStats?.draft_receipts || 0 }}</div>
              <div class="overview-label">草稿</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon processing">
              <el-icon size="32"><Loading /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ receiptStats?.submitted_receipts || 0 }}</div>
              <div class="overview-label">已提交</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon completed">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ receiptStats?.approved_receipts || 0 }}</div>
              <div class="overview-label">已审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="入库单号">
          <el-input
            v-model="searchForm.receipt_no"
            placeholder="请输入入库单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="采购订单号">
          <el-input
            v-model="searchForm.purchase_order_no"
            placeholder="请输入采购订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="供应商">
          <el-select v-model="searchForm.supplier_id" placeholder="选择供应商" clearable style="width: 200px">
            <el-option
              v-for="supplier in suppliers"
              :key="supplier.id"
              :label="supplier.name"
              :value="supplier.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="单据状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="草稿" :value="PurchaseReceiptStatus.DRAFT" />
            <el-option label="已提交" :value="PurchaseReceiptStatus.SUBMITTED" />
            <el-option label="已审核" :value="PurchaseReceiptStatus.APPROVED" />
            <el-option label="已拒绝" :value="PurchaseReceiptStatus.REJECTED" />
            <el-option label="已取消" :value="PurchaseReceiptStatus.CANCELLED" />
          </el-select>
        </el-form-item>

        <el-form-item label="仓库">
          <el-select v-model="searchForm.warehouse_id" placeholder="选择仓库" clearable style="width: 120px">
            <el-option
              v-for="warehouse in warehouses"
              :key="warehouse.id"
              :label="warehouse.name"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="searchReceipts">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 入库单列表 -->
    <el-card class="receipt-list-card">
      <template #header>
        <span>采购入库单列表 (共 {{ total }} 条)</span>
        <div class="header-actions">
          <el-button-group>
            <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
              <el-icon><List /></el-icon>
              列表视图
            </el-button>
            <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
              <el-icon><Grid /></el-icon>
              卡片视图
            </el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="purchaseReceipts" style="width: 100%" v-loading="loading">
          <el-table-column type="selection" width="55" />

          <el-table-column prop="receipt_no" label="入库单号" width="150">
            <template #default="{ row }">
              <el-link type="primary" @click="viewReceiptDetail(row)">
                {{ row.receipt_no }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column prop="purchase_order_no" label="采购订单号" width="150">
            <template #default="{ row }">
              <el-link
                v-if="row.purchase_order_no"
                type="success"
                @click="viewPurchaseOrder(row.purchase_order_no)"
              >
                {{ row.purchase_order_no }}
              </el-link>
              <span v-else class="text-muted">手动创建</span>
            </template>
          </el-table-column>

          <el-table-column prop="supplier_name" label="供应商" width="200" />

          <el-table-column prop="warehouse_name" label="入库仓库" width="120" />

          <el-table-column prop="total_quantity" label="商品种类" width="100">
            <template #default="{ row }">
              <span>{{ row.items?.length || 0 }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="quantity" label="入库数量" width="120">
            <template #default="{ row }">
              <span>{{ row.items?.reduce((sum: number, item: PurchaseReceiptItem) => sum + (item.quantity || 0), 0) || 0 }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="单据状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="receipt_date" label="入库日期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.receipt_date) }}
            </template>
          </el-table-column>

          <el-table-column prop="created_by" label="创建人" width="100" />

          <el-table-column prop="created_at" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="220" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button size="small" @click="viewReceiptDetail(row)">查看</el-button>

                <!-- 草稿状态：显示提交按钮 -->
                <template v-if="row.status === PurchaseReceiptStatus.DRAFT">
                  <el-button size="small" type="success" @click="submitReceipt(row)">提交</el-button>
                </template>

                <!-- 已提交状态：显示撤回按钮 -->
                <template v-if="row.status === PurchaseReceiptStatus.SUBMITTED">
                  <el-button size="small" type="warning" @click="recallReceipt(row)">撤回</el-button>
                </template>

                <!-- 已审核状态：显示确定入库按钮 -->
                <template v-if="row.status === PurchaseReceiptStatus.APPROVED">
                  <el-button size="small" type="primary" @click="confirmReceipt(row)">确定入库</el-button>
                </template>

                <!-- 更多操作菜单 -->
                <el-dropdown @command="(command: string) => handleReceiptAction(command, row)" v-if="row.status !== PurchaseReceiptStatus.APPROVED && row.status !== PurchaseReceiptStatus.REJECTED && row.status !== PurchaseReceiptStatus.RECEIVED && row.status !== PurchaseReceiptStatus.CANCELLED">
                  <el-button size="small">
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- 草稿状态：编辑 -->
                    <el-dropdown-item command="edit" v-if="row.status === PurchaseReceiptStatus.DRAFT">编辑</el-dropdown-item>

                    <!-- 已提交状态：审核选项 -->
                    <el-dropdown-item command="approve" v-if="row.status === PurchaseReceiptStatus.SUBMITTED">审核通过</el-dropdown-item>
                    <el-dropdown-item command="reject" v-if="row.status === PurchaseReceiptStatus.SUBMITTED">审核拒绝</el-dropdown-item>

                    <!-- 分隔线 -->
                    <el-dropdown-item divided command="cancel">取消</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div class="receipts-grid">
          <div
            v-for="receipt in purchaseReceipts"
            :key="receipt.id"
            class="receipt-card"
          >
            <div class="card-header">
              <div class="receipt-info">
                <h4 class="receipt-no">{{ receipt.receipt_no }}</h4>
                <p class="supplier-name">{{ receipt.supplier_name }}</p>
                <p class="warehouse-name">仓库: {{ receipt.warehouse_name }}</p>
              </div>
              <div class="receipt-status">
                <el-tag :type="getStatusTagType(receipt.status)" size="small">
                  {{ getStatusLabel(receipt.status) }}
                </el-tag>
              </div>
            </div>

            <div class="card-content">
              <div class="receipt-details">
                <div class="detail-row" v-if="receipt.purchase_order_no">
                  <span class="label">采购订单:</span>
                  <el-link type="success" size="small" @click="viewPurchaseOrder(receipt.purchase_order_no)">
                    {{ receipt.purchase_order_no }}
                  </el-link>
                </div>
                <div class="detail-row">
                  <span class="label">商品种类:</span>
                  <span class="value">{{ receipt.items?.length || 0 }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">已入库:</span>
                  <span class="value">{{ receipt.items?.reduce((sum: number, item: PurchaseReceiptItem) => sum + (item.quantity || 0), 0) || 0 }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">入库日期:</span>
                  <span class="value">{{ formatDate(receipt.receipt_date) }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">创建人:</span>
                  <span class="value">{{ receipt.created_by }}</span>
                </div>
              </div>


            </div>

            <div class="card-actions">
              <el-button size="small" @click="viewReceiptDetail(receipt)">查看详情</el-button>

              <!-- 草稿状态：显示提交按钮 -->
              <template v-if="receipt.status === PurchaseReceiptStatus.DRAFT">
                <el-button size="small" type="success" @click="submitReceipt(receipt)">提交</el-button>
              </template>

              <!-- 已提交状态：显示撤回按钮 -->
              <template v-if="receipt.status === PurchaseReceiptStatus.SUBMITTED">
                <el-button size="small" type="warning" @click="recallReceipt(receipt)">撤回</el-button>
              </template>

              <!-- 更多操作菜单 -->
              <el-dropdown @command="(command: string) => handleReceiptAction(command, receipt)" v-if="receipt.status !== PurchaseReceiptStatus.APPROVED && receipt.status !== PurchaseReceiptStatus.REJECTED && receipt.status !== PurchaseReceiptStatus.CANCELLED">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- 草稿状态：编辑 -->
                    <el-dropdown-item command="edit" v-if="receipt.status === PurchaseReceiptStatus.DRAFT">编辑</el-dropdown-item>

                    <!-- 已提交状态：审核选项 -->
                    <el-dropdown-item command="approve" v-if="receipt.status === PurchaseReceiptStatus.SUBMITTED">审核通过</el-dropdown-item>
                    <el-dropdown-item command="reject" v-if="receipt.status === PurchaseReceiptStatus.SUBMITTED">审核拒绝</el-dropdown-item>

                    <!-- 分隔线 -->
                    <el-dropdown-item divided command="cancel">取消</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>



    <!-- 新建/编辑入库单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingReceipt ? '编辑入库单' : '新建入库单'"
      width="1000px"
      @close="resetReceiptForm"
    >
      <el-form :model="receiptForm" :rules="receiptRules" ref="receiptFormRef" label-width="100px">
        <!-- 入库方式选择 -->
        <el-form-item label="入库方式">
          <el-radio-group v-model="receiptForm.receipt_type" @change="onReceiptTypeChange">
            <el-radio value="manual">手动创建</el-radio>
            <el-radio value="from_order">从采购订单创建</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 选择采购订单 -->
        <el-form-item
          v-if="receiptForm.receipt_type === 'from_order'"
          label="采购订单"
          prop="purchase_order_id"
        >
          <el-select
            v-model="receiptForm.purchase_order_id"
            placeholder="选择采购订单"
            style="width: 100%"
            filterable
            @change="onPurchaseOrderChange"
            :loading="loadingOrders"
          >
            <el-option
              v-for="order in availablePurchaseOrders"
              :key="order.id"
              :label="`${order.order_no} - ${order.supplier_name} (¥${order.total_amount})`"
              :value="order.id"
            />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplier_id">
              <el-select
                v-model="receiptForm.supplier_id"
                placeholder="选择供应商"
                style="width: 100%"
                :disabled="receiptForm.receipt_type === 'from_order'"
              >
                <el-option
                  v-for="supplier in suppliers"
                  :key="supplier.id"
                  :label="supplier.name"
                  :value="supplier.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库仓库" prop="warehouse_id">
              <el-select v-model="receiptForm.warehouse_id" placeholder="选择仓库" style="width: 100%">
                <el-option
                  v-for="warehouse in warehouses"
                  :key="warehouse.id"
                  :label="warehouse.name"
                  :value="warehouse.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="入库日期" prop="receipt_date">
              <el-date-picker
                v-model="receiptForm.receipt_date"
                type="date"
                placeholder="选择入库日期"
                format="YYYY年MM月DD日"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购订单号">
              <el-input
                v-model="receiptForm.purchase_order_no"
                placeholder="关联的采购订单号（可选）"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注信息">
          <el-input
            v-model="receiptForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <!-- 入库商品列表 -->
        <el-form-item label="入库商品" required>
          <div class="receipt-items">
            <div class="items-header">
              <el-button
                size="small"
                @click="addReceiptItem"
                v-if="receiptForm.receipt_type === 'manual'"
              >
                <el-icon><Plus /></el-icon>
                添加商品
              </el-button>
              <div v-if="receiptForm.receipt_type === 'from_order'" class="order-mode-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>从采购订单创建模式：商品列表由所选订单自动生成，不可手动添加</span>
              </div>
            </div>

            <div style="overflow-x: auto; width: 100%; margin-top: 10px; padding: 0 4px;">
              <div class="custom-table">
                <!-- 表头 -->
                <div class="table-header">
                  <div class="header-cell product-name">商品名称</div>
                  <div class="header-cell quantity">数量</div>
                  <div class="header-cell batch">批次号</div>
                  <div class="header-cell production-date">生产日期</div>
                  <div class="header-cell expiry-date">到期日期</div>
                  <div class="header-cell actions">操作</div>
                </div>

                <!-- 表格内容 -->
                <div class="table-body">
                  <div v-for="(row, index) in receiptForm.items" :key="index" class="table-row">
                    <div class="table-cell product-name">
                      <el-select
                        v-model="row.product_id"
                        placeholder="选择商品"
                        filterable
                        @change="onProductChange(row)"
                        :disabled="receiptForm.receipt_type === 'from_order'"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="product in products"
                          :key="product.id"
                          :label="`${product.name} (${product.sku})`"
                          :value="product.id"
                        />
                      </el-select>
                    </div>

                    <div class="table-cell quantity">
                      <el-input-number
                        v-model="row.quantity"
                        :min="0"
                        :max="receiptForm.receipt_type === 'from_order' ? row.max_quantity : undefined"
                        style="width: 100%"
                        size="small"
                        :placeholder="receiptForm.receipt_type === 'from_order' ? `最多${row.max_quantity || 0}` : '请输入数量'"
                      />
                    </div>

                    <div class="table-cell batch">
                      <el-input
                        v-model="row.batch_no"
                        placeholder="请输入批次号"
                        style="width: 100%"
                        size="small"
                      />
                    </div>

                    <div class="table-cell production-date">
                      <el-date-picker
                        v-model="row.production_date"
                        type="date"
                        placeholder="选择生产日期"
                        style="width: 100%"
                        size="small"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                      />
                    </div>

                    <div class="table-cell expiry-date">
                      <el-date-picker
                        v-model="row.expiry_date"
                        type="date"
                        placeholder="选择到期日期"
                        style="width: 100%"
                        size="small"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                      />
                    </div>

                    <div class="table-cell actions">
                      <el-button
                        size="small"
                        type="danger"
                        @click="removeReceiptItem(index)"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveReceipt" :loading="saving">
          {{ editingReceipt ? '更新' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 入库单详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="入库单详情"
      width="1000px"
    >
      <div v-if="selectedReceipt" class="receipt-detail">
        <div class="detail-header">
          <div class="receipt-basic-info">
            <h3>
              {{ selectedReceipt.receipt_no }}
              <el-tag :type="getStatusTagType(selectedReceipt.status)" style="margin-left: 12px;">
                {{ getStatusLabel(selectedReceipt.status) }}
              </el-tag>
            </h3>
          </div>
        </div>

        <div class="detail-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="供应商">{{ selectedReceipt.supplier_name }}</el-descriptions-item>
            <el-descriptions-item label="入库仓库">{{ selectedReceipt.warehouse_name }}</el-descriptions-item>
            <el-descriptions-item label="采购订单号">
              <el-link
                v-if="selectedReceipt.purchase_order_no"
                type="success"
                @click="viewPurchaseOrder(selectedReceipt.purchase_order_no)"
              >
                {{ selectedReceipt.purchase_order_no }}
              </el-link>
              <span v-else>手动创建</span>
            </el-descriptions-item>
            <el-descriptions-item label="入库日期">{{ formatDate(selectedReceipt.receipt_date) }}</el-descriptions-item>
            <el-descriptions-item label="创建人">{{ selectedReceipt.created_by }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDateTime(selectedReceipt.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="备注信息" :span="2">{{ selectedReceipt.remark || '无' }}</el-descriptions-item>
          </el-descriptions>

          <div class="items-section">
            <h4>入库商品明细</h4>
            <el-table :data="selectedReceipt.items" style="width: 100%; max-width: 100%;">
              <el-table-column prop="product_name" label="商品名称" width="160" show-overflow-tooltip />
              <el-table-column prop="product_sku" label="商品SKU" width="100" />
              <el-table-column prop="quantity" label="数量" width="60" align="center">
                <template #default="{ row }">
                  <span>{{ row.quantity || 0 }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="batch_no" label="批次号" width="100">
                <template #default="{ row }">
                  <span>{{ row.batch_no || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="production_date" label="生产日期" width="100">
                <template #default="{ row }">
                  <span>{{ row.production_date ? formatDate(row.production_date) : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="expiry_date" label="到期日期" width="100">
                <template #default="{ row }">
                  <span>{{ row.expiry_date ? formatDate(row.expiry_date) : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="80" align="center">
                <template #default="{ row }">
                  <el-tag
                    :type="row.quantity > 0 ? 'success' : 'info'"
                    size="small"
                  >
                    {{ row.quantity > 0 ? '已入库' : '未入库' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Download,
  Tickets,
  Clock,
  Loading,
  CircleCheck,
  Search,
  List,
  Grid,
  ArrowDown,
  InfoFilled
} from '@element-plus/icons-vue'
import { purchaseReceiptApi, PurchaseReceiptStatus, type PurchaseReceipt, type PurchaseReceiptItem } from '@/api/purchase'
import { warehouseApi, type Warehouse } from '@/api/warehouses'
import { supplierApi, type Supplier } from '@/api/suppliers'
import { productApi, type Product } from '@/api/products'
import { purchaseApi, type PurchaseOrder, PurchaseOrderStatus } from '@/api/purchase'
import { inventoryTransferApi } from '@/api/inventory'

// 本地类型定义
interface ReceiptStats {
  total_receipts: number
  draft_receipts: number
  submitted_receipts: number
  approved_receipts: number
  rejected_receipts: number
  this_month_receipts: number
}

interface SearchForm {
  receipt_no: string
  purchase_order_no: string
  supplier_id: number | null
  status: PurchaseReceiptStatus | ''
  warehouse_id: number | null
}

interface ReceiptForm {
  receipt_type: 'manual' | 'from_order'
  purchase_order_id: number | null
  supplier_id: number | null
  warehouse_id: number | null
  receipt_date: string | null
  purchase_order_no: string
  remark: string
  items: {
    product_id: number
    product_name: string
    product_sku?: string
    quantity: number
    batch_no?: string
    production_date?: string
    expiry_date?: string
    max_quantity?: number  // 从采购订单创建时的最大可入库数量
    order_quantity?: number  // 订单数量
    received_quantity?: number  // 已收货数量
    order_line_number?: number  // 采购订单行号
  }[]
}

// 响应式数据
const purchaseReceipts = ref<PurchaseReceipt[]>([])
const suppliers = ref<Supplier[]>([])
const products = ref<Product[]>([])
const warehouses = ref<Warehouse[]>([])
const availablePurchaseOrders = ref<PurchaseOrder[]>([])
const loading = ref(false)
const saving = ref(false)
const loadingOrders = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')

const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const editingReceipt = ref<PurchaseReceipt | null>(null)
const selectedReceipt = ref<PurchaseReceipt | null>(null)

const receiptStats = ref<ReceiptStats>({
  total_receipts: 0,
  draft_receipts: 0,
  submitted_receipts: 0,
  approved_receipts: 0,
  rejected_receipts: 0,
  this_month_receipts: 0
})

const searchForm = reactive<SearchForm>({
  receipt_no: '',
  purchase_order_no: '',
  supplier_id: null,
  status: '',
  warehouse_id: null
})

const receiptForm = reactive<ReceiptForm>({
  receipt_type: 'manual',
  purchase_order_id: null,
  supplier_id: null,
  warehouse_id: null,
  receipt_date: null,
  purchase_order_no: '',
  remark: '',
  items: []
})

const receiptFormRef = ref()

// 表单验证规则
const receiptRules = {
  purchase_order_id: [
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (receiptForm.receipt_type === 'from_order' && !value) {
          callback(new Error('请选择采购订单'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  warehouse_id: [{ required: true, message: '请选择入库仓库', trigger: 'change' }],
  receipt_date: [{ required: true, message: '请选择入库日期', trigger: 'change' }]
}

// 方法
const fetchPurchaseReceipts = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      receipt_no: searchForm.receipt_no || undefined,
      purchase_order_no: searchForm.purchase_order_no || undefined,
      supplier_id: searchForm.supplier_id || undefined,
      warehouse_id: searchForm.warehouse_id || undefined,
      status: searchForm.status || undefined
    }

    const response = await purchaseReceiptApi.getPurchaseReceipts(params)

    // 修复：由于axios响应拦截器返回response.data，所以response就是数据对象
    purchaseReceipts.value = (response as any)?.items || []
    total.value = (response as any)?.total || 0

    console.log('✅ 采购入库单数据加载成功:', {
      count: purchaseReceipts.value.length,
      total: total.value
    })
  } catch (error: any) {
    console.error('❌ 获取入库单失败:', error)
    console.error('❌ 错误详情:', {
      message: error.message,
      response: error.response,
      status: error.response?.status,
      data: error.response?.data
    })
    ElMessage.error(error.response?.data?.detail || '获取入库单失败')
  } finally {
    loading.value = false
  }
}

const fetchReceiptStats = async () => {
  try {
    const response = await purchaseReceiptApi.getPurchaseReceiptStats()

    // 修复：由于axios响应拦截器返回response.data，所以response就是数据对象
    receiptStats.value = {
      total_receipts: (response as any)?.total_receipts || 0,
      draft_receipts: (response as any)?.draft_receipts || 0,
      submitted_receipts: (response as any)?.submitted_receipts || 0,
      approved_receipts: (response as any)?.approved_receipts || 0,
      rejected_receipts: (response as any)?.rejected_receipts || 0,
      this_month_receipts: (response as any)?.this_month_receipts || 0
    }

    console.log('📈 统计数据加载成功:', receiptStats.value)

    console.log('📈 设置统计数据:', receiptStats.value)
  } catch (error: any) {
    console.error('❌ 获取统计数据失败:', error)
    console.error('❌ 统计错误详情:', {
      message: error.message,
      response: error.response,
      status: error.response?.status,
      data: error.response?.data
    })
    // 设置默认值
    receiptStats.value = {
      total_receipts: 0,
      draft_receipts: 0,
      submitted_receipts: 0,
      approved_receipts: 0,
      rejected_receipts: 0,
      this_month_receipts: 0
    }
  }
}

const fetchSuppliers = async () => {
  try {
    const response = await supplierApi.getSuppliers()
    // 修复：由于axios响应拦截器返回response.data，所以response就是数据对象
    suppliers.value = (response as any)?.items || (response as any) || []

    console.log('🏭 供应商数据加载成功:', {
      count: suppliers.value.length,
      suppliers: suppliers.value
    })
  } catch (error: any) {
    console.error('❌ 获取供应商失败:', error)
    suppliers.value = []
  }
}

const fetchProducts = async () => {
  try {
    const response = await productApi.getProducts()
    // 修复：由于axios响应拦截器返回response.data，所以response就是数据对象
    products.value = (response as any)?.items || (response as any) || []

    console.log('📦 产品数据加载成功:', {
      count: products.value.length,
      products: products.value
    })
  } catch (error: any) {
    console.error('❌ 获取商品失败:', error)
    products.value = []
  }
}

const fetchWarehouses = async () => {
  try {
    // 使用仓库API获取活跃仓库列表
    const response = await warehouseApi.getWarehouses({ is_active: true, status: 'active' })
    warehouses.value = (response || []).filter((w: any) => w.is_active && w.status === 'active')

    console.log('🏢 仓库数据加载成功:', {
      count: warehouses.value.length,
      warehouses: warehouses.value
    })
  } catch (error: any) {
    console.error('❌ 获取仓库失败:', error)
    // 如果失败，尝试使用库存调拨API作为回退
    try {
      const fallbackResponse = await inventoryTransferApi.getActiveWarehouses()
      const mappedWarehouses = (fallbackResponse || []).map((w: any) => ({
        id: w.id,
        name: w.name,
        address: w.address || '',
        total_capacity_m3: 0,
        used_capacity_m3: 0,
        is_active: w.is_active,
        status: w.status
      }))
      warehouses.value = mappedWarehouses
      console.log('🏢 使用回退API获取仓库数据:', {
        count: warehouses.value.length,
        warehouses: warehouses.value
      })
    } catch (fallbackError) {
      console.error('❌ 回退API也失败:', fallbackError)
      warehouses.value = []
    }
  }
}
// 获取可用的采购订单（有剩余未入库数量的订单）
const fetchAvailablePurchaseOrders = async () => {
  try {
    loadingOrders.value = true

    // 使用专门的API获取有剩余数量的采购订单
    const orders = await purchaseApi.getAvailablePurchaseOrdersForReceipt()
    availablePurchaseOrders.value = orders

    console.log('📋 可用采购订单加载成功:', {
      count: availablePurchaseOrders.value.length,
      orders: availablePurchaseOrders.value.map(order => ({
        id: order.id,
        order_no: order.order_no,
        supplier_name: order.supplier_name,
        items_count: order.items.length,
        remaining_items: order.items.filter(item => (item.received_quantity || 0) < item.quantity).length
      }))
    })
  } catch (error: any) {
    console.error('❌ 获取采购订单失败:', error)
    availablePurchaseOrders.value = []
  } finally {
    loadingOrders.value = false
  }
}

// 入库方式改变时的处理
const onReceiptTypeChange = (type: 'manual' | 'from_order') => {
  if (type === 'from_order') {
    // 切换到从订单创建模式，获取可用订单
    fetchAvailablePurchaseOrders()
    // 清空手动输入的数据
    receiptForm.supplier_id = null
    receiptForm.items = []
  } else {
    // 切换到手动创建模式，清空订单相关数据
    receiptForm.purchase_order_id = null
    receiptForm.purchase_order_no = ''
    receiptForm.supplier_id = null
    receiptForm.items = []
  }
}

// 选择采购订单时的处理
const onPurchaseOrderChange = async (orderId: number) => {
  if (!orderId) return

  try {
    const order = availablePurchaseOrders.value.find(o => o.id === orderId)
    if (!order) return

    // 自动填充供应商信息和订单信息
    receiptForm.purchase_order_id = order.id
    receiptForm.supplier_id = order.supplier_id
    receiptForm.purchase_order_no = order.order_no

    // 自动填充商品明细（只包含未完全入库的商品）
    receiptForm.items = order.items
      .map(item => {
        const receivedQuantity = item.received_quantity || 0
        const remainingQuantity = item.quantity - receivedQuantity
        return {
          ...item,
          remainingQuantity // 添加剩余可入库数量
        }
      })
      .filter(item => item.remainingQuantity > 0) // 只包含剩余数量大于0的商品
      .map(item => ({
        product_id: item.product_id,
        product_name: item.product_name,
        product_sku: item.product_sku || '',
        quantity: item.remainingQuantity, // 本次入库数量，默认为剩余数量，用户可以修改
        batch_no: '', // 批次号
        max_quantity: item.remainingQuantity, // 最大可入库数量
        order_quantity: item.quantity, // 订单数量
        received_quantity: item.received_quantity || 0, // 已收货数量
        order_line_number: item.line_number // 采购订单行号
      }))

    console.log('📦 已选择采购订单:', {
      orderNo: order.order_no,
      supplier: order.supplier_name,
      itemsCount: receiptForm.items.length,
      items: receiptForm.items.map(item => ({
        product_name: item.product_name,
        line_number: item.order_line_number,
        quantity: item.quantity
      }))
    })
  } catch (error) {
    console.error('❌ 处理采购订单选择失败:', error)
    ElMessage.error('处理采购订单失败')
  }
}





const searchReceipts = () => {
  // 实现搜索逻辑
  fetchPurchaseReceipts()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    receipt_no: '',
    purchase_order_no: '',
    supplier_id: null,
    status: '',
    warehouse_id: null
  })
  fetchPurchaseReceipts()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchPurchaseReceipts()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchPurchaseReceipts()
}

const viewReceiptDetail = (receipt: PurchaseReceipt) => {
  selectedReceipt.value = receipt
  showDetailDialog.value = true
}

const viewPurchaseOrder = (orderNo: string) => {
  ElMessage.info(`查看采购订单: ${orderNo}`)
  // 这里可以跳转到采购订单详情页面
}



// 提交入库单
const submitReceipt = async (receipt: PurchaseReceipt) => {
  try {
    const currentUser = 'admin' // 这里应该从用户状态获取
    await purchaseReceiptApi.submitPurchaseReceipt(receipt.id, currentUser)
    ElMessage.success('入库单提交成功')
    await fetchPurchaseReceipts()
    await fetchReceiptStats()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '提交失败')
  }
}

// 撤回入库单
const recallReceipt = async (receipt: PurchaseReceipt) => {
  try {
    await purchaseReceiptApi.recallPurchaseReceipt(receipt.id)
    ElMessage.success('入库单撤回成功')
    await fetchPurchaseReceipts()
    await fetchReceiptStats()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '撤回失败')
  }
}

// 确认入库
const confirmReceipt = async (receipt: PurchaseReceipt) => {
  try {
    await ElMessageBox.confirm(
      '确定要将此入库单的商品添加到库存中吗？此操作不可撤销。',
      '确认入库',
      {
        type: 'warning',
        confirmButtonText: '确定入库',
        cancelButtonText: '取消'
      }
    )

    await purchaseReceiptApi.confirmPurchaseReceipt(receipt.id!, '当前用户') // TODO: 从用户上下文获取
    ElMessage.success('入库确认成功，商品已添加到库存')
    await fetchPurchaseReceipts()
    await fetchReceiptStats()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('确认入库失败:', error)
      ElMessage.error('确认入库失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

// 审核入库单
const approveReceipt = async (receipt: PurchaseReceipt, approved: boolean) => {
  try {
    const currentUser = 'admin' // 这里应该从用户状态获取
    const action = approved ? '通过' : '拒绝'

    await ElMessageBox.confirm(
      `确认${action}此入库单吗？`,
      `审核${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: approved ? 'success' : 'warning'
      }
    )

    await purchaseReceiptApi.approvePurchaseReceipt(receipt.id, approved)
    ElMessage.success(`入库单审核${action}成功`)
    await fetchPurchaseReceipts()
    await fetchReceiptStats()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.response?.data?.detail || `审核${approved ? '通过' : '拒绝'}失败`)
    }
  }
}



const handleReceiptAction = async (command: string, receipt: PurchaseReceipt) => {
  try {
    switch (command) {
      case 'edit':
        editReceipt(receipt)
        return

      case 'approve':
        await approveReceipt(receipt, true)
        return

      case 'reject':
        await approveReceipt(receipt, false)
        return

      case 'cancel':
        await ElMessageBox.confirm(
          '确定要取消这个入库单吗？',
          '确认取消',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        await purchaseReceiptApi.cancelPurchaseReceipt(receipt.id)
        ElMessage.success('入库单取消成功')

        // 重新获取数据
        await fetchPurchaseReceipts()
        await fetchReceiptStats()
        break
    }
  } catch (error) {
    // 用户取消操作
  }
}

const editReceipt = (receipt: PurchaseReceipt) => {
  editingReceipt.value = receipt
  Object.assign(receiptForm, {
    supplier_id: receipt.supplier_id,
    warehouse_id: receipt.warehouse_id,
    receipt_date: receipt.receipt_date || new Date().toISOString().split('T')[0],
    purchase_order_no: receipt.purchase_order_no || '',
    remark: receipt.remark || '',
    items: [...(receipt.items || [])]
  })
  showCreateDialog.value = true
}



const addReceiptItem = () => {
  receiptForm.items.push({
    product_id: 0,
    product_name: '',
    product_sku: '',
    quantity: 0,
    batch_no: '',
    production_date: '',
    expiry_date: ''
  })
}

const removeReceiptItem = (index: number) => {
  receiptForm.items.splice(index, 1)
}

const onProductChange = (item: any) => {
  const product = products.value.find(p => p.id === item.product_id)
  if (product) {
    item.product_name = product.name
    item.product_sku = product.sku
  }
}

const saveReceipt = async () => {
  if (!receiptFormRef.value) return

  try {
    await receiptFormRef.value.validate()

    if (receiptForm.items.length === 0) {
      ElMessage.warning('请至少添加一个入库商品')
      return
    }

    saving.value = true

    if (editingReceipt.value) {
      // 更新现有入库单
      const updateData = {
        purchase_order_id: receiptForm.purchase_order_id || undefined,
        supplier_id: receiptForm.supplier_id!,
        warehouse_id: receiptForm.warehouse_id!,
        receipt_date: receiptForm.receipt_date || undefined,
        purchase_order_no: receiptForm.purchase_order_no,
        remark: receiptForm.remark,
        items: receiptForm.items
      }

      console.log('📦 更新入库单数据:', updateData)
      console.log('📦 入库单明细:', receiptForm.items)

      await purchaseReceiptApi.updatePurchaseReceipt(editingReceipt.value.id, updateData)
      ElMessage.success('入库单更新成功')
    } else {
      // 新增入库单
      const createData = {
        purchase_order_id: receiptForm.purchase_order_id || undefined,
        supplier_id: receiptForm.supplier_id!,
        warehouse_id: receiptForm.warehouse_id!,
        receipt_date: receiptForm.receipt_date ? new Date(receiptForm.receipt_date).toISOString() : undefined,
        purchase_order_no: receiptForm.purchase_order_no,
        remark: receiptForm.remark,
        // created_by 将由后端从当前用户会话中获取
        items: receiptForm.items
      }

      console.log('📦 保存入库单数据:', createData)
      console.log('📦 入库单明细:', receiptForm.items)

      await purchaseReceiptApi.createPurchaseReceipt(createData)
      ElMessage.success('入库单创建成功')
    }

    // 重新获取数据
    await fetchPurchaseReceipts()
    await fetchReceiptStats()

    showCreateDialog.value = false
    resetReceiptForm()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetReceiptForm = () => {
  editingReceipt.value = null
  Object.assign(receiptForm, {
    receipt_type: 'manual',
    purchase_order_id: null,
    supplier_id: null,
    warehouse_id: null,
    receipt_date: null,
    purchase_order_no: '',
    remark: '',
    items: []
  })
  if (receiptFormRef.value) {
    receiptFormRef.value.clearValidate()
  }
}

const exportReceipts = () => {
  ElMessage.success('入库单导出成功')
}

// 辅助方法
const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    'draft': '草稿',
    'submitted': '已提交',
    'approved': '已审核',
    'rejected': '已拒绝',
    'received': '已入库',
    'cancelled': '已取消'
  }
  return labelMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'draft': 'info',
    'submitted': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'received': 'primary',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}





const formatDate = (dateString: string | undefined) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string | undefined) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  console.log('🚀 采购入库单页面已挂载，开始加载数据...')
  fetchPurchaseReceipts()
  fetchReceiptStats()
  fetchSuppliers()
  fetchProducts()
  fetchWarehouses()
})
</script>

<style>
.purchase-receipt-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 入库单概览 */
.receipt-overview {
  margin-bottom: 12px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.total {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.pending {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-icon.processing {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.completed {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 入库单列表卡片 */
.receipt-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 调整入库单列表卡片头部的内边距并设置布局 */
.receipt-list-card .el-card__header {
  padding: 14px 20px !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 文本样式 */
.text-muted {
  color: #909399;
  font-style: italic;
}

.text-warning {
  color: #E6A23C;
  font-weight: 600;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.receipts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.receipt-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.receipt-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.receipt-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.supplier-name,
.warehouse-name {
  margin: 0 0 2px 0;
  font-size: 12px;
  opacity: 0.9;
}

.card-content {
  padding: 16px;
}

.receipt-details {
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-row .label {
  color: #606266;
}

.detail-row .value {
  color: #2c3e50;
}



.card-actions {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 入库单详情 */
.receipt-detail {
  padding: 20px 0;
  max-width: 100%;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.receipt-basic-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 20px;
}

.receipt-progress {
  text-align: right;
}

.progress-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.progress-value {
  font-size: 24px;
  font-weight: 700;
  color: #409EFF;
}

.detail-content {
  margin-top: 20px;
}

.items-section {
  margin-top: 20px;
  overflow: hidden;
}

.items-section h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 16px;
}

.items-section .el-table {
  max-width: 100%;
  overflow-x: auto;
}

/* 订单选择 */
.order-selection {
  max-height: 400px;
  overflow-y: auto;
}

/* 入库商品表单 */
.receipt-items {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.items-header {
  margin-bottom: 16px;
}

/* 入库商品表格样式优化 */
.receipt-items .el-table {
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.receipt-items .el-table .el-table__header-wrapper {
  border-radius: 6px 6px 0 0;
}

.receipt-items .el-table .el-table__header {
  background-color: #f8f9fa;
}

.receipt-items .el-table .el-table__header th {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

.receipt-items .el-table .el-input-number {
  width: 100%;
}

.receipt-items .el-table .el-select {
  width: 100%;
}

.receipt-items .el-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 确保表格列宽度与表单字段对齐 */
.receipt-items .el-table .el-table__cell {
  padding: 8px 12px;
}

.receipt-items .el-table .el-input-number .el-input__inner {
  text-align: center;
}

/* 表格横向滚动容器 */
.table-scroll-container {
  overflow-x: auto;
  overflow-y: visible;
  width: 100%;
  margin-top: 10px;
}

/* 入库商品表格固定宽度 */
.receipt-items-table {
  min-width: 1000px !important;
  width: 1000px !important;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 强制列宽度 */
.receipt-items-table .el-table__header-wrapper,
.receipt-items-table .el-table__body-wrapper {
  width: 1200px !important;
}

/* 强制表格宽度和滚动 */
.el-dialog .el-table {
  min-width: 1200px !important;
  width: 1200px !important;
  table-layout: fixed !important;
}

.el-dialog .el-table .el-table__header-wrapper,
.el-dialog .el-table .el-table__body-wrapper {
  width: 1200px !important;
}

.el-dialog .el-table .el-table__header,
.el-dialog .el-table .el-table__body {
  width: 1200px !important;
}

/* 确保列宽度不被压缩 */
.el-dialog .el-table .el-table__cell {
  white-space: nowrap !important;
  overflow: visible !important;
}

/* 专门针对宽表格的样式 */
.wide-table {
  min-width: 1200px !important;
  width: 1200px !important;
  table-layout: fixed !important;
}

.wide-table .el-table__header-wrapper {
  width: 1200px !important;
}

.wide-table .el-table__body-wrapper {
  width: 1200px !important;
}

.wide-table .el-table__header {
  width: 1200px !important;
}

.wide-table .el-table__body {
  width: 1200px !important;
}

.wide-table .el-table__header-wrapper table,
.wide-table .el-table__body-wrapper table {
  width: 1200px !important;
  table-layout: fixed !important;
}

/* 自定义表格样式 */
.custom-table {
  width: 100%;
  max-width: 920px;
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 200px 100px 120px 150px 150px 90px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 200px 100px 120px 150px 150px 90px;
  border-bottom: 1px solid #ebeef5;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: #f5f7fa;
}

.header-cell {
  padding: 12px 8px;
  font-weight: 600;
  color: #606266;
  text-align: center;
  border-right: 1px solid #ebeef5;
}

.header-cell:last-child {
  border-right: none;
}

.table-cell {
  padding: 8px;
  border-right: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  min-height: 60px;
}

.table-cell:last-child {
  border-right: none;
  justify-content: center;
}

.table-cell.product-name {
  width: 200px;
  min-width: 200px;
}

.table-cell.quantity {
  width: 100px;
  min-width: 100px;
}

.table-cell.batch {
  width: 120px;
  min-width: 120px;
}

.table-cell.production-date {
  width: 150px;
  min-width: 150px;
}

.table-cell.expiry-date {
  width: 150px;
  min-width: 150px;
}

.table-cell.actions {
  width: 90px;
  min-width: 90px;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

/* 按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.el-button--success {
  background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
  border: none;
}

.el-button--success:hover {
  background: linear-gradient(135deg, #5daf34 0%, #7bc143 100%);
}

/* 链接样式 */
.el-link {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .receipt-overview .el-col {
    margin-bottom: 16px;
  }

  .receipts-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  .purchase-receipt-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .receipts-grid {
    grid-template-columns: 1fr;
  }

  .detail-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .receipt-progress {
    text-align: left;
  }
}

.order-mode-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;

  .el-icon {
    color: #409eff;
  }
}

/* 表格操作按钮样式 */
.table-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: nowrap;

  .el-button {
    margin: 0;
    min-width: auto;
    padding: 5px 8px;
  }

  .el-dropdown {
    .el-button {
      padding: 5px 8px;
    }
  }
}


</style>
