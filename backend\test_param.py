#!/usr/bin/env python3
"""
测试参数解析
"""

import requests

def test_param():
    """测试参数解析"""
    
    try:
        print("🧪 测试参数解析...")
        
        # 测试不同的参数格式
        urls = [
            "http://127.0.0.1:8000/api/sales/?available_only=true",
            "http://127.0.0.1:8000/api/sales/?available_only=True", 
            "http://127.0.0.1:8000/api/sales/?available_only=1",
        ]
        
        for url in urls:
            print(f"\n📡 测试URL: {url}")
            response = requests.get(url)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    print(f"   ✅ 返回数组，长度: {len(data)}")
                elif isinstance(data, dict) and 'items' in data:
                    print(f"   ❌ 返回分页对象，items长度: {len(data['items'])}")
                else:
                    print(f"   ❓ 未知格式: {type(data)}")
            else:
                print(f"   ❌ 请求失败: {response.text}")
        
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    test_param()
