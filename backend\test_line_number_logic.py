#!/usr/bin/env python3
"""
测试销售订单行号逻辑
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

from sqlalchemy import text
from app.core.database import get_db
from app.models.sales import SalesOrder, SalesOrderItem, SalesOutbound, SalesOutboundItem
from app.services.sales_service import SalesService

def test_line_number_logic():
    """测试销售订单行号逻辑"""
    
    db = next(get_db())
    service = SalesService(db)
    
    try:
        print("🧪 测试销售订单行号逻辑...")
        
        # 1. 检查数据库表结构
        print("\n1. 检查数据库表结构:")
        result = db.execute(text("SELECT * FROM sales_outbound_items LIMIT 1"))
        columns = list(result.keys())
        print(f"   sales_outbound_items 字段: {columns}")
        
        if 'sales_order_line_number' in columns:
            print("   ✅ sales_order_line_number 字段存在")
        else:
            print("   ❌ sales_order_line_number 字段不存在")
            return
        
        if 'sales_order_item_id' in columns:
            print("   ❌ sales_order_item_id 字段仍然存在")
            return
        else:
            print("   ✅ sales_order_item_id 字段已删除")
        
        # 2. 检查有行号的出库单明细
        print("\n2. 检查有行号的出库单明细:")
        result = db.execute(text("""
            SELECT 
                soi.id,
                soi.product_name,
                soi.sales_order_line_number,
                so.sales_order_id,
                so.sales_order_no
            FROM sales_outbound_items soi
            JOIN sales_outbounds so ON soi.outbound_id = so.id
            WHERE soi.sales_order_line_number IS NOT NULL
        """))
        
        items_with_line_number = result.fetchall()
        print(f"   找到 {len(items_with_line_number)} 个有行号的出库单明细:")
        for item in items_with_line_number:
            print(f"   - 明细ID {item.id}: {item.product_name}")
            print(f"     销售订单: {item.sales_order_no} (ID: {item.sales_order_id})")
            print(f"     行号: {item.sales_order_line_number}")
        
        # 3. 验证行号匹配逻辑
        if items_with_line_number:
            print("\n3. 验证行号匹配逻辑:")
            test_item = items_with_line_number[0]
            
            # 查找对应的销售订单明细
            order_item = db.query(SalesOrderItem).filter(
                SalesOrderItem.order_id == test_item.sales_order_id,
                SalesOrderItem.line_number == test_item.sales_order_line_number
            ).first()
            
            if order_item:
                print(f"   ✅ 成功匹配到销售订单明细:")
                print(f"     订单明细ID: {order_item.id}")
                print(f"     商品: {order_item.product_name}")
                print(f"     行号: {order_item.line_number}")
                print(f"     订单数量: {order_item.quantity}")
                print(f"     已出库数量: {order_item.shipped_quantity}")
            else:
                print(f"   ❌ 无法匹配到销售订单明细")
        
        # 4. 测试更新已出库数量的逻辑
        print("\n4. 测试更新已出库数量的逻辑:")
        
        # 查找一个已完成的出库单
        outbound = db.query(SalesOutbound).filter(
            SalesOutbound.status == 'completed',
            SalesOutbound.sales_order_id.isnot(None)
        ).first()
        
        if outbound:
            print(f"   找到已完成出库单: {outbound.outbound_no}")
            print(f"   关联销售订单ID: {outbound.sales_order_id}")
            
            # 手动调用更新方法
            print("   手动调用更新方法...")
            service._update_sales_order_shipped_quantity(outbound)
            db.commit()
            
            # 检查结果
            print("   检查更新结果:")
            for item in outbound.items:
                if item.sales_order_line_number:
                    order_item = db.query(SalesOrderItem).filter(
                        SalesOrderItem.order_id == outbound.sales_order_id,
                        SalesOrderItem.line_number == item.sales_order_line_number
                    ).first()
                    
                    if order_item:
                        print(f"     {order_item.product_name}: {order_item.shipped_quantity}/{order_item.quantity}")
        else:
            print("   没有找到合适的已完成出库单")
        
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_line_number_logic()
