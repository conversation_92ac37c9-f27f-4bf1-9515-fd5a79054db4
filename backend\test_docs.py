#!/usr/bin/env python3
"""
测试API文档
"""

import requests

def test_docs():
    """测试API文档"""
    
    try:
        print("🧪 测试API文档...")
        
        # 获取OpenAPI文档
        print("\n📡 获取OpenAPI文档...")
        response = requests.get("http://127.0.0.1:8000/openapi.json")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            openapi_doc = response.json()
            paths = openapi_doc.get("paths", {})
            
            print(f"   总路径数量: {len(paths)}")
            
            # 查找销售相关的路径
            sales_paths = [path for path in paths.keys() if "/api/sales" in path]
            print(f"   销售相关路径数量: {len(sales_paths)}")
            
            print("\n   销售相关路径:")
            for path in sorted(sales_paths):
                methods = list(paths[path].keys())
                print(f"     {path}: {methods}")
                
                # 检查是否有我们的新路径
                if "orders/available" in path:
                    print(f"       ✅ 找到可用订单路径: {path}")
        
        print("\n✅ API文档测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    test_docs()
