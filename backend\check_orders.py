#!/usr/bin/env python3
"""
检查销售订单状态
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

from sqlalchemy import text
from app.core.database import get_db
from app.models.sales import SalesOrder, SalesOrderItem
from app.services.sales_service import SalesService

def check_orders():
    """检查销售订单状态"""
    
    db = next(get_db())
    service = SalesService(db)
    
    try:
        print("🔍 检查销售订单状态...")
        
        # 1. 检查所有销售订单
        print("\n1. 所有销售订单:")
        all_orders = db.query(SalesOrder).all()
        print(f"   总订单数: {len(all_orders)}")
        
        for order in all_orders:
            print(f"   - {order.order_no}: {order.status} (客户: {order.customer.name if order.customer else '未知'})")
        
        # 2. 检查已审核的订单
        print("\n2. 已审核的订单:")
        approved_orders = db.query(SalesOrder).filter(
            SalesOrder.status == 'approved'
        ).all()
        print(f"   已审核订单数: {len(approved_orders)}")
        
        for order in approved_orders:
            print(f"   - {order.order_no}: {order.customer.name if order.customer else '未知'}")
            for item in order.items:
                shipped = item.shipped_quantity or 0
                remaining = item.quantity - shipped
                print(f"     * {item.product_name}: 订单{item.quantity} - 已发{shipped} = 剩余{remaining}")
        
        # 3. 测试API
        print("\n3. 测试可用订单API:")
        available_orders = service.get_available_sales_orders()
        print(f"   可用订单数: {len(available_orders)}")
        
        for order in available_orders:
            print(f"   - 订单ID: {order.get('id')}")
            print(f"     订单号: {order.get('order_no')}")
            print(f"     客户: {order.get('customer_name')}")
            print(f"     剩余明细: {len(order.get('remaining_items', []))}")
        
        print("\n✅ 检查完成")
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    check_orders()
