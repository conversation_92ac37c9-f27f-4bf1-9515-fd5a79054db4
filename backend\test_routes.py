#!/usr/bin/env python3
"""
测试路由定义
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

def test_routes():
    """测试路由定义"""
    
    try:
        print("🧪 测试路由定义...")
        
        # 导入路由模块
        from app.api import sales
        
        print(f"   销售路由模块导入成功")
        print(f"   路由对象: {sales.router}")
        
        # 获取路由信息
        routes = sales.router.routes
        print(f"   总路由数量: {len(routes)}")
        
        # 查找我们的路由
        for route in routes:
            if hasattr(route, 'path'):
                path = route.path
                methods = getattr(route, 'methods', [])
                
                if 'available-orders' in path:
                    print(f"   ✅ 找到可用订单路由: {path} {methods}")
                
                if 'outbound' in path:
                    print(f"   出库相关路由: {path} {methods}")
        
        print("\n✅ 路由测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_routes()
