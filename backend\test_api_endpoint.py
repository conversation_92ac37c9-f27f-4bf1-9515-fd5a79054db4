#!/usr/bin/env python3
"""
测试API端点
"""

import requests
import json

def test_api_endpoint():
    """测试API端点"""
    
    try:
        print("🧪 测试API端点...")
        
        # 测试可用订单API
        url = "http://127.0.0.1:8000/api/sales/?available_only=true"
        print(f"\n📡 调用API: {url}")
        
        response = requests.get(url)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API调用成功")
            print(f"   数据类型: {type(data)}")
            
            if isinstance(data, list):
                print(f"   订单数量: {len(data)}")
                for i, order in enumerate(data[:3]):
                    print(f"   订单 {i+1}:")
                    print(f"     ID: {order.get('id')}")
                    print(f"     订单号: {order.get('order_no')}")
                    print(f"     客户: {order.get('customer_name')}")
                    print(f"     剩余明细: {len(order.get('remaining_items', []))}")
            else:
                print(f"   数据内容: {data}")
        else:
            print(f"   ❌ API调用失败")
            print(f"   响应内容: {response.text}")
        
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    test_api_endpoint()
