#!/usr/bin/env python3
"""
简单API测试
"""

import requests

def test_simple_api():
    """测试简单API"""
    
    try:
        print("🧪 测试简单API...")
        
        # 测试根路径
        print("\n📡 测试根路径...")
        response = requests.get("http://127.0.0.1:8000/")
        print(f"   状态码: {response.status_code}")
        
        # 测试健康检查
        print("\n📡 测试健康检查...")
        response = requests.get("http://127.0.0.1:8000/health")
        print(f"   状态码: {response.status_code}")
        
        # 测试销售订单API
        print("\n📡 测试销售订单API...")
        response = requests.get("http://127.0.0.1:8000/api/sales/?page=1&page_size=1")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 销售订单API正常")
        else:
            print(f"   ❌ 销售订单API异常: {response.text}")
        
        print("\n✅ 简单API测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    test_simple_api()
