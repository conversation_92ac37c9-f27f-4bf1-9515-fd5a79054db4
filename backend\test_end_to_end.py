#!/usr/bin/env python3
"""
端到端测试：从创建出库单到完成出库的完整流程
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

from sqlalchemy import text
from app.core.database import get_db
from app.models.sales import SalesOrder, SalesOrderItem, SalesOutbound, SalesOutboundItem
from app.services.sales_service import SalesService
from app.schemas.sales import SalesOutboundCreate, SalesOutboundItemCreate

def test_end_to_end():
    """端到端测试：完整的出库流程"""
    
    db = next(get_db())
    service = SalesService(db)
    
    try:
        print("🎯 端到端测试：完整的出库流程...")
        
        # 1. 获取一个有剩余数量的销售订单
        print("\n1. 获取可用销售订单:")
        available_orders = service.get_available_sales_orders()
        
        if not available_orders:
            print("   ❌ 没有可用的销售订单")
            return
        
        test_order = available_orders[0]
        print(f"   选择测试订单: {test_order['order_no']}")
        print(f"   客户: {test_order['customer_name']}")
        print(f"   剩余明细数量: {len(test_order['remaining_items'])}")

        # 显示剩余明细
        for item in test_order['remaining_items']:
            print(f"     * 行{item['line_number']}: {item['product_name']} - 剩余{item['quantity']}件")
        
        # 2. 创建出库单（模拟前端行为）
        print(f"\n2. 创建出库单:")
        
        # 选择第一个剩余明细进行出库
        test_item = test_order['remaining_items'][0]
        outbound_qty = min(test_item['quantity'], 1)  # 出库1件或剩余数量

        outbound_data = SalesOutboundCreate(
            sales_order_id=test_order['id'],
            sales_order_no=test_order['order_no'],
            customer_id=test_order['customer_id'],
            status='pending',
            items=[
                SalesOutboundItemCreate(
                    sales_order_line_number=test_item['line_number'],
                    product_id=test_item['product_id'],
                    warehouse_id=1,  # 假设仓库ID为1
                    quantity=outbound_qty,
                    batch_no=None
                )
            ]
        )
        
        print(f"   创建出库单，商品: {test_item['product_name']}")
        print(f"   行号: {test_item['line_number']}")
        print(f"   出库数量: {outbound_qty}")
        
        # 创建出库单
        created_outbound = service.create_sales_outbound(outbound_data, "test_user")
        print(f"   ✅ 出库单创建成功: {created_outbound.outbound_no}")
        
        # 3. 验证出库单明细
        print(f"\n3. 验证出库单明细:")
        for item in created_outbound.items:
            print(f"   商品: {item.product_name}")
            print(f"   销售订单行号: {item.sales_order_line_number}")
            print(f"   应出数量: {item.quantity}")
            print(f"   实出数量: {item.outbound_quantity}")
        
        # 4. 提交和审核出库单
        print(f"\n4. 提交和审核出库单:")
        try:
            # 提交出库单
            submitted_outbound = service.submit_sales_outbound(created_outbound.id, "test_user")
            print(f"   ✅ 出库单提交成功，状态: {submitted_outbound.status}")

            # 审核出库单
            approved_outbound = service.approve_sales_outbound(created_outbound.id, "test_user")
            print(f"   ✅ 出库单审核成功，状态: {approved_outbound.status}")

            # 完成出库单
            completed_outbound = service.complete_sales_outbound(created_outbound.id, "test_user")
            print(f"   ✅ 出库单完成成功，状态: {completed_outbound.status}")
        except Exception as e:
            print(f"   ❌ 出库单处理失败: {str(e)}")
            # 如果是库存不足，我们继续测试其他逻辑
            if "库存不足" in str(e) or "扣减库存失败" in str(e):
                print("   继续测试其他逻辑...")
                completed_outbound = created_outbound
            else:
                raise
        
        # 5. 验证销售订单明细更新
        print(f"\n5. 验证销售订单明细更新:")
        
        # 重新查询销售订单明细
        order_item = db.query(SalesOrderItem).filter(
            SalesOrderItem.order_id == test_order['id'],
            SalesOrderItem.line_number == test_item['line_number']
        ).first()
        
        if order_item:
            print(f"   商品: {order_item.product_name}")
            print(f"   订单数量: {order_item.quantity}")
            print(f"   已出库数量: {order_item.shipped_quantity}")
            
            expected_shipped = (order_item.shipped_quantity or 0)
            if completed_outbound.status == 'completed':
                expected_shipped += outbound_qty
            
            print(f"   预期已出库数量: {expected_shipped}")
        
        # 6. 再次获取可用销售订单，验证过滤逻辑
        print(f"\n6. 验证过滤逻辑:")
        updated_available_orders = service.get_available_sales_orders()
        
        # 查找同一个订单
        updated_test_order = None
        for order in updated_available_orders:
            if order['id'] == test_order['id']:
                updated_test_order = order
                break

        if updated_test_order:
            print(f"   订单 {updated_test_order['order_no']} 仍在可用列表中")
            print(f"   更新后剩余明细数量: {len(updated_test_order['remaining_items'])}")

            # 查找测试的明细
            updated_test_item = None
            for item in updated_test_order['remaining_items']:
                if item['line_number'] == test_item['line_number']:
                    updated_test_item = item
                    break
            
            if updated_test_item:
                print(f"   行{updated_test_item['line_number']} 剩余数量: {updated_test_item['quantity']}")
                expected_remaining = test_item['quantity']
                if completed_outbound.status == 'completed':
                    expected_remaining -= outbound_qty
                print(f"   预期剩余数量: {expected_remaining}")
            else:
                print(f"   行{test_item['line_number']} 已完全出库，不在剩余列表中")
        else:
            print(f"   订单 {test_order['order_no']} 已完全出库，不在可用列表中")
        
        print("\n✅ 端到端测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_end_to_end()
