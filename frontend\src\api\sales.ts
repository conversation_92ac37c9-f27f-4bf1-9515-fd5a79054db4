/**
 * 销售管理API - 包含销售订单、销售出库单、销售退货单
 */

import api from './index'

// ==================== 销售订单相关类型定义 ====================
export type SalesOrderStatus = 'draft' | 'submitted' | 'approved' | 'rejected' | 'shipped' | 'cancelled'

export type Platform = 'manual' | 'taobao' | 'tmall' | 'jd' | 'pdd' | 'douyin' | 'xiaohongshu' | 'wechat'

export interface SalesOrderItem {
  id?: number
  order_id?: number
  line_number: number
  product_id: number
  product_name?: string
  product_sku?: string
  quantity: number
  unit_price: number
  total_price: number
  shipped_quantity?: number
  created_at?: string
  updated_at?: string
}

export interface SalesOrder {
  id?: number
  order_no?: string
  customer_id: number
  customer_name?: string
  total_amount: number
  status: SalesOrderStatus
  delivery_date?: string
  delivery_address?: string
  sales_person?: string
  platform: Platform
  original_order_no?: string
  remark?: string
  created_at?: string
  updated_at?: string
  items: SalesOrderItem[]
}

export interface SalesOrderStats {
  total_orders: number
  draft_orders: number
  submitted_orders: number
  approved_orders: number
  rejected_orders: number
  processing_orders: number
  shipped_orders: number
  cancelled_orders: number
  total_amount: number
  average_order_amount: number
}

export interface SalesOrderQuery {
  order_no?: string
  customer_id?: number
  status?: SalesOrderStatus
  sales_person?: string
  platform?: Platform
  start_date?: string
  end_date?: string
}

export interface SalesOrderListResponse {
  items: SalesOrder[]
  total: number
  page: number
  page_size: number
  pages: number
}

export interface SalesOrderCreate {
  customer_id: number
  total_amount: number
  status?: SalesOrderStatus
  delivery_date?: string
  delivery_address?: string
  sales_person?: string
  platform?: Platform
  original_order_no?: string
  remark?: string
  items: SalesOrderItem[]
}

export interface SalesOrderUpdate {
  customer_id?: number
  total_amount?: number
  status?: SalesOrderStatus
  delivery_date?: string
  delivery_address?: string
  sales_person?: string
  platform?: Platform
  original_order_no?: string
  remark?: string
}

// ==================== 销售出库单相关类型定义 ====================
export type SalesOutboundStatus = 'pending' | 'submitted' | 'approved' | 'rejected' | 'partial' | 'completed' | 'cancelled'

export interface SalesOutboundItem {
  id?: number
  outbound_id?: number
  sales_order_line_number?: number
  product_id: number
  product_name?: string
  product_sku?: string
  warehouse_id: number
  warehouse_name?: string
  quantity: number
  outbound_quantity?: number
  created_at?: string
}

export interface SalesOutbound {
  id?: number
  outbound_no?: string
  sales_order_id?: number
  sales_order_no?: string
  customer_id: number
  customer_name?: string
  status: SalesOutboundStatus
  outbound_date?: string
  created_by?: string
  operator?: string
  remark?: string
  created_at?: string
  updated_at?: string
  items: SalesOutboundItem[]
}

export interface SalesOutboundCreate {
  sales_order_id?: number
  sales_order_no?: string
  customer_id: number
  status?: SalesOutboundStatus
  outbound_date?: string
  operator?: string
  remark?: string
  items: SalesOutboundItem[]
}

export interface AvailableSalesOrder {
  id: number
  order_no: string
  customer_id: number
  customer_name: string
  total_amount: number
  status: string
  delivery_date?: string
  created_at: string
  remaining_items: Array<{
    id: number
    line_number: number
    product_id: number
    product_name: string
    product_sku: string
    quantity: number  // 剩余未发货数量
    original_quantity: number  // 原始订单数量
    shipped_quantity: number  // 已发货数量
    unit_price: number
    total_price: number  // 按剩余数量计算的金额
  }>
}

// ==================== 销售退货单相关类型定义 ====================
// 销售退货单状态枚举
export enum SalesReturnStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  RETURNED = 'returned',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 销售退货单明细接口
export interface SalesReturnItem {
  id?: number
  sales_return_id?: number
  product_id: number
  product_name: string
  product_sku?: string
  return_quantity: number
  unit_price: number
  total_price: number
  quality_issue?: string
  created_at?: string
  updated_at?: string
}

// 销售退货单接口
export interface SalesReturn {
  id?: number
  return_no: string
  sales_order_id?: number
  customer_id: number
  return_date: string
  reason: string
  total_amount: number
  status: SalesReturnStatus
  remark?: string

  // 审核信息
  submitted_at?: string
  submitted_by?: string
  approved_at?: string
  approved_by?: string
  approval_note?: string

  // 退货信息
  returned_at?: string
  returned_by?: string

  // 时间戳
  created_at?: string
  updated_at?: string
  created_by?: string
  updated_by?: string

  // 关联信息
  customer_name?: string
  sales_order_no?: string

  // 明细
  items: SalesReturnItem[]
}

// 创建销售退货单数据
export interface SalesReturnCreate {
  return_no?: string
  sales_order_id?: number
  customer_id: number
  return_date: string
  reason: string
  total_amount: number
  remark?: string
  items: Omit<SalesReturnItem, 'id' | 'sales_return_id' | 'created_at' | 'updated_at'>[]
}

// 更新销售退货单数据
export interface SalesReturnUpdate {
  return_no?: string
  sales_order_id?: number
  customer_id?: number
  return_date?: string
  reason?: string
  total_amount?: number
  remark?: string
  items?: Omit<SalesReturnItem, 'id' | 'sales_return_id' | 'created_at' | 'updated_at'>[]
}

// 状态更新数据
export interface SalesReturnStatusUpdate {
  status: SalesReturnStatus
  note?: string
}

// 统计数据接口
export interface SalesReturnStats {
  total_returns: number
  draft_returns: number
  submitted_returns: number
  approved_returns: number
  returned_returns: number
  completed_returns: number
  total_amount: number
}

// 查询参数接口
export interface SalesReturnQuery {
  return_no?: string
  customer_id?: number
  status?: SalesReturnStatus
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// ==================== API接口 ====================

// 销售订单API
export const salesApi = {
  /**
   * 获取销售订单列表
   */
  async getSalesOrders(params: {
    page?: number
    page_size?: number
    order_no?: string
    customer_id?: number
    status?: SalesOrderStatus
    sales_person?: string
    platform?: Platform
    start_date?: string
    end_date?: string
  } = {}): Promise<SalesOrderListResponse> {
    return await api.get('/api/sales/', { params })
  },

  /**
   * 获取销售订单统计
   */
  async getSalesStats(): Promise<SalesOrderStats> {
    return await api.get('/api/sales/stats')
  },

  /**
   * 获取单个销售订单
   */
  async getSalesOrder(orderId: number): Promise<SalesOrder> {
    return await api.get(`/api/sales/${orderId}`)
  },

  /**
   * 创建销售订单
   */
  async createSalesOrder(data: SalesOrderCreate): Promise<SalesOrder> {
    return await api.post('/api/sales/', data)
  },

  /**
   * 更新销售订单
   */
  async updateSalesOrder(orderId: number, data: SalesOrderCreate): Promise<SalesOrder> {
    return await api.put(`/api/sales/${orderId}`, data)
  },

  /**
   * 删除销售订单
   */
  async deleteSalesOrder(orderId: number): Promise<{ message: string }> {
    return await api.delete(`/api/sales/${orderId}`)
  },

  /**
   * 更新订单状态
   */
  async updateOrderStatus(orderId: number, status: SalesOrderStatus): Promise<{ message: string; status: string }> {
    return await api.patch(`/api/sales/${orderId}/status`, null, {
      params: { status }
    })
  }
}

// 销售出库单API
export const salesOutboundApi = {
  /**
   * 获取销售出库单统计
   */
  async getSalesOutboundStats(): Promise<any> {
    return await api.get('/api/sales/outbounds/stats')
  },

  /**
   * 获取销售出库单列表
   */
  async getSalesOutbounds(params: {
    page?: number
    page_size?: number
    outbound_no?: string
    customer_id?: number
    warehouse_id?: number
    status?: SalesOutboundStatus
    start_date?: string
    end_date?: string
  } = {}): Promise<SalesOutbound[]> {
    return await api.get('/api/sales/outbounds/', { params })
  },

  /**
   * 获取单个销售出库单
   */
  async getSalesOutbound(outboundId: number): Promise<SalesOutbound> {
    return await api.get(`/api/sales/outbounds/${outboundId}`)
  },

  /**
   * 创建销售出库单
   */
  async createSalesOutbound(data: SalesOutboundCreate): Promise<SalesOutbound> {
    return await api.post('/api/sales/outbounds/', data)
  },

  /**
   * 更新销售出库单
   */
  async updateSalesOutbound(outboundId: number, data: SalesOutboundCreate): Promise<SalesOutbound> {
    return await api.put(`/api/sales/outbounds/${outboundId}`, data)
  },

  /**
   * 删除销售出库单
   */
  async deleteSalesOutbound(outboundId: number): Promise<{ message: string }> {
    return await api.delete(`/api/sales/outbounds/${outboundId}`)
  },

  /**
   * 提交销售出库单
   */
  async submitSalesOutbound(outboundId: number): Promise<{ message: string; status: string }> {
    return await api.post(`/api/sales/outbounds/${outboundId}/submit`)
  },

  /**
   * 审核通过销售出库单
   */
  async approveSalesOutbound(outboundId: number): Promise<{ message: string; status: string }> {
    return await api.post(`/api/sales/outbounds/${outboundId}/approve`)
  },

  /**
   * 审核拒绝销售出库单
   */
  async rejectSalesOutbound(outboundId: number, rejectReason: string): Promise<{ message: string; status: string }> {
    return await api.post(`/api/sales/outbounds/${outboundId}/reject`, null, {
      params: { reject_reason: rejectReason }
    })
  },

  /**
   * 撤销销售出库单（从已提交状态回到草稿状态）
   */
  async revokeSalesOutbound(outboundId: number): Promise<{ message: string; status: string }> {
    return await api.post(`/api/sales/outbounds/${outboundId}/revoke`)
  },

  /**
   * 取消发货
   */
  async cancelDelivery(outboundId: number, cancelReason: string): Promise<{ message: string; status: string }> {
    return await api.post(`/api/sales/outbounds/${outboundId}/cancel-delivery`, null, {
      params: { cancel_reason: cancelReason }
    })
  },

  /**
   * 确认完成销售出库单
   */
  async completeSalesOutbound(outboundId: number): Promise<{ message: string; status: string }> {
    return await api.post(`/api/sales/outbounds/${outboundId}/complete`)
  },

  /**
   * 获取可用的销售订单
   */
  async getAvailableSalesOrders(customerId?: number): Promise<AvailableSalesOrder[]> {
    const params = customerId ? { customer_id: customerId } : {}
    return await api.get('/api/sales/outbounds/available-orders/', { params })
  }
}

// 销售退货单API
export const salesReturnApi = {
  // 获取统计信息
  async getStats(): Promise<SalesReturnStats> {
    return await api.get('/api/sales/returns/stats')
  },

  // 获取销售退货单列表
  async getSalesReturns(params?: SalesReturnQuery): Promise<SalesReturn[]> {
    return await api.get('/api/sales/returns/', { params })
  },

  // 获取销售退货单详情
  async getSalesReturn(id: number): Promise<SalesReturn> {
    return await api.get(`/api/sales/returns/${id}`)
  },

  // 创建销售退货单
  async createSalesReturn(data: SalesReturnCreate): Promise<SalesReturn> {
    return await api.post('/api/sales/returns/', data)
  },

  // 更新销售退货单
  async updateSalesReturn(id: number, data: SalesReturnUpdate): Promise<SalesReturn> {
    return await api.put(`/api/sales/returns/${id}`, data)
  },

  // 删除销售退货单
  async deleteSalesReturn(id: number): Promise<{ message: string }> {
    return await api.delete(`/api/sales/returns/${id}`)
  },

  // 提交销售退货单
  async submitSalesReturn(id: number): Promise<{ message: string }> {
    return await api.post(`/api/sales/returns/${id}/submit`)
  },

  // 审核销售退货单
  async approveSalesReturn(id: number, data: SalesReturnStatusUpdate): Promise<{ message: string }> {
    return await api.post(`/api/sales/returns/${id}/approve`, data)
  },

  // 确认退货
  async returnSalesReturn(id: number): Promise<{ message: string }> {
    return await api.post(`/api/sales/returns/${id}/return`)
  },

  // 完成销售退货单
  async completeSalesReturn(id: number): Promise<{ message: string }> {
    return await api.post(`/api/sales/returns/${id}/complete`)
  }
}

// 默认导出销售订单API（保持向后兼容）
export default salesApi
