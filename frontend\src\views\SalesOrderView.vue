<template>
  <div class="sales-order-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>销售订单</h2>
        <p class="page-description">管理销售订单，跟踪销售进度和客户需求</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCreateOrder">
          <el-icon><Plus /></el-icon>
          新建销售订单
        </el-button>
        <el-button @click="syncPlatformOrders" :loading="syncing">
          <el-icon><Refresh /></el-icon>
          同步订单
        </el-button>
        <el-button @click="exportOrders">
          <el-icon><Download /></el-icon>
          导出订单
        </el-button>

      </div>
    </div>

    <!-- 订单概览 -->
    <el-row :gutter="20" class="order-overview">
      <el-col :span="6">
        <el-card class="overview-card clickable" @click="filterByStatus('')">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon size="32"><Document /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ orderStats.total_orders }}</div>
              <div class="overview-label">订单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card clickable" @click="filterByStatus('draft')">
          <div class="overview-content">
            <div class="overview-icon draft">
              <el-icon size="32"><Document /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ orderStats.draft_orders }}</div>
              <div class="overview-label">草稿</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card clickable" @click="filterByStatus('submitted')">
          <div class="overview-content">
            <div class="overview-icon submitted">
              <el-icon size="32"><Van /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ orderStats.submitted_orders }}</div>
              <div class="overview-label">已提交</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card clickable" @click="filterByStatus('approved')">
          <div class="overview-content">
            <div class="overview-icon approved">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ orderStats.approved_orders }}</div>
              <div class="overview-label">已审核</div>
            </div>
          </div>
        </el-card>
      </el-col>


    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">

      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="订单号">
          <el-input v-model="searchForm.order_no" placeholder="输入订单号" style="width: 150px" />
        </el-form-item>
        <el-form-item label="客户">
          <el-select v-model="searchForm.customer_id" placeholder="选择客户" clearable style="width: 150px">
            <el-option
              v-for="customer in customers"
              :key="customer.id"
              :label="customer.name"
              :value="customer.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="草稿" value="draft" />
            <el-option label="已提交" value="submitted" />
            <el-option label="已审核" value="approved" />
            <el-option label="已发货" value="shipped" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="销售员">
          <el-input v-model="searchForm.sales_person" placeholder="销售员" style="width: 120px" />
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="searchForm.platform" placeholder="选择平台" clearable style="width: 120px">
            <el-option label="手工录入" value="manual" />
            <el-option label="淘宝" value="taobao" />
            <el-option label="天猫" value="tmall" />
            <el-option label="京东" value="jd" />
            <el-option label="拼多多" value="pdd" />
            <el-option label="抖音" value="douyin" />
            <el-option label="小红书" value="xiaohongshu" />
            <el-option label="微信" value="wechat" />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="searchOrders">
            搜索
          </el-button>
          <el-button @click="resetSearch">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <div class="table-header">
        <h3>订单列表</h3>
        <div class="table-actions">
          <el-button-group>
            <el-button
              :type="viewMode === 'table' ? 'primary' : 'default'"
              :icon="List"
              @click="viewMode = 'table'"
            >
              表格视图
            </el-button>
            <el-button
              :type="viewMode === 'card' ? 'primary' : 'default'"
              :icon="Grid"
              @click="viewMode = 'card'"
            >
              卡片视图
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'" style="overflow-x: auto;">
        <el-table
          :data="salesOrders"
          v-loading="loading"
          stripe
          style="width: 100%; min-width: 1390px;"
        >
          <el-table-column prop="order_no" label="订单号" width="180" show-overflow-tooltip />
          <el-table-column prop="original_order_no" label="原单号" width="180" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.original_order_no || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="platform" label="平台" width="70" align="center">
            <template #default="{ row }">
              <el-tag :type="getPlatformTagType(row.platform)" size="small">
                {{ getPlatformLabel(row.platform) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="customer_name" label="客户" width="200" show-overflow-tooltip />
          <el-table-column prop="total_amount" label="订单金额" width="100" align="right">
            <template #default="{ row }">
              <span class="amount">¥{{ row.total_amount.toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sales_person" label="销售员" width="120" show-overflow-tooltip />
          <el-table-column prop="delivery_date" label="交货日期" width="100">
            <template #default="{ row }">
              {{ formatDate(row.delivery_date) }}
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button size="small" @click.stop="viewOrderDetail(row)">查看</el-button>

                <!-- 已提交状态：显示撤回按钮（仅手工建单） -->
                <template v-if="row.status === 'submitted' && row.platform === 'manual'">
                  <el-button size="small" type="warning" @click.stop="withdrawOrder(row)">撤回</el-button>
                </template>



                <!-- 草稿状态：显示提交按钮（仅手工录入） -->
                <template v-if="row.status === 'draft' && row.platform === 'manual'">
                  <el-button size="small" type="success" @click.stop="submitOrder(row)">提交</el-button>
                </template>







                <!-- 更多操作菜单 -->
                <el-dropdown @command="(command: string) => handleOrderAction(command, row)" v-if="row.status !== 'shipped' && row.status !== 'cancelled' && row.status !== 'approved' && row.status !== 'rejected'">
                  <el-button size="small" @click.stop>
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <!-- 草稿状态：编辑 -->
                      <el-dropdown-item command="edit" v-if="row.status === 'draft'">编辑</el-dropdown-item>

                      <!-- 已提交状态：审核通过、审核拒绝（仅手工建单） -->
                      <template v-if="row.status === 'submitted'">
                        <el-dropdown-item command="approve">审核通过</el-dropdown-item>
                        <el-dropdown-item command="reject" v-if="row.platform === 'manual'">审核拒绝</el-dropdown-item>
                      </template>

                      <!-- 分隔线 -->
                      <el-dropdown-item divided command="cancel" v-if="row.status === 'submitted'">取消订单</el-dropdown-item>
                      <el-dropdown-item divided command="delete" v-if="row.status === 'draft'">删除订单</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <el-row :gutter="20" v-loading="loading">
          <el-col :span="8" v-for="order in salesOrders" :key="order.id" style="margin-bottom: 20px">
            <el-card class="order-card">
              <div class="order-header">
                <div class="order-no">{{ order.order_no }}</div>
                <el-tag :type="getStatusTagType(order.status)" size="small">
                  {{ getStatusLabel(order.status) }}
                </el-tag>
              </div>
              <div class="order-content">
                <p><strong>客户:</strong> {{ order.customer_name }}</p>
                <p><strong>金额:</strong> <span class="amount">¥{{ order.total_amount.toFixed(2) }}</span></p>
                <p><strong>平台:</strong> {{ getPlatformLabel(order.platform) }}</p>
                <p><strong>原单号:</strong> {{ order.original_order_no || '-' }}</p>
                <p><strong>销售员:</strong> {{ order.sales_person || '-' }}</p>
                <p><strong>交货日期:</strong> {{ formatDate(order.delivery_date) }}</p>
              </div>
              <div class="order-actions">
                <el-button size="small" @click.stop="viewOrderDetail(order)">查看详情</el-button>

                <!-- 草稿状态：显示提交按钮（仅手工录入） -->
                <template v-if="order.status === 'draft' && order.platform === 'manual'">
                  <el-button size="small" type="success" @click.stop="submitOrder(order)">提交</el-button>
                </template>



                <!-- 已提交状态：显示审核按钮（所有平台） -->
                <template v-if="order.status === 'submitted'">
                  <el-button size="small" type="primary" @click.stop="approveOrder(order)">审核通过</el-button>
                </template>






              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchSalesOrders"
          @current-change="fetchSalesOrders"
        />
      </div>
    </el-card>

    <!-- 创建/编辑订单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingOrder ? '编辑销售订单' : '新建销售订单'"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="orderFormRef"
        :model="orderForm"
        :rules="orderRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户" prop="customer_id">
              <el-select
                v-model="orderForm.customer_id"
                placeholder="选择客户"
                style="width: 100%"
                filterable
              >
                <el-option
                  v-for="customer in customers"
                  :key="customer.id"
                  :label="customer.name"
                  :value="customer.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售员" prop="sales_person">
              <el-input
                v-model="orderForm.sales_person"
                placeholder="输入销售员姓名"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="交货日期" prop="delivery_date">
              <el-date-picker
                v-model="orderForm.delivery_date"
                type="date"
                placeholder="选择交货日期"
                format="YYYY年MM月DD日"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售平台" prop="platform">
              <el-select
                v-model="orderForm.platform"
                placeholder="选择销售平台"
                style="width: 100%"
              >
                <el-option label="手工录入" value="manual" />
                <el-option label="淘宝" value="taobao" />
                <el-option label="天猫" value="tmall" />
                <el-option label="京东" value="jd" />
                <el-option label="拼多多" value="pdd" />
                <el-option label="抖音" value="douyin" />
                <el-option label="小红书" value="xiaohongshu" />
                <el-option label="微信" value="wechat" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="配送地址">
          <el-input
            v-model="orderForm.delivery_address"
            type="textarea"
            :rows="2"
            placeholder="请输入配送地址"
          />
        </el-form-item>

        <el-form-item label="原始订单号" v-if="orderForm.platform !== 'manual'">
          <el-input
            v-model="orderForm.original_order_no"
            placeholder="输入平台订单号"
          />
        </el-form-item>

        <el-form-item label="备注信息">
          <el-input
            v-model="orderForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <!-- 销售商品列表 -->
        <el-form-item label="销售商品" required>
          <div class="sales-items">
            <div class="items-header">
              <el-button size="small" @click="addOrderItem">
                <el-icon><Plus /></el-icon>
                添加商品
              </el-button>
            </div>

            <el-table :data="orderForm.items" style="width: 100%; margin-top: 10px;" border>
              <el-table-column label="商品名称" min-width="200">
                <template #default="{ row, $index }">
                  <el-select
                    v-model="row.product_id"
                    placeholder="选择商品"
                    filterable
                    @change="onProductChange(row, $index)"
                  >
                    <el-option
                      v-for="product in products"
                      :key="product.id"
                      :label="`${product.name} (${product.sku})`"
                      :value="product.id"
                    />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="销售数量" width="120">
                <template #default="{ row, $index }">
                  <el-input-number
                    v-model="row.quantity"
                    :min="1"
                    @change="calculateItemTotal(row, $index)"
                  />
                </template>
              </el-table-column>

              <el-table-column label="销售单价" width="150">
                <template #default="{ row, $index }">
                  <el-input-number
                    v-model="row.unit_price"
                    :min="0"
                    :precision="2"
                    @change="calculateItemTotal(row, $index)"
                  />
                </template>
              </el-table-column>

              <el-table-column label="小计" width="120">
                <template #default="{ row }">
                  <span class="amount">¥{{ row.total_price.toFixed(2) }}</span>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="80">
                <template #default="{ $index }">
                  <el-button size="small" type="danger" @click="removeOrderItem($index)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <div class="total-section">
              <div class="total-row">
                <span class="total-label">订单总金额:</span>
                <span class="total-amount">¥{{ calculateTotalAmount().toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSaveOrder" :loading="saving">
            {{ editingOrder ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="销售订单详情"
      width="800px"
    >
      <div v-if="selectedOrder" class="order-detail">
        <div class="detail-header">
          <div class="order-basic-info">
            <h3>{{ selectedOrder.order_no }}</h3>
            <el-tag :type="getStatusTagType(selectedOrder.status)">
              {{ getStatusLabel(selectedOrder.status) }}
            </el-tag>
          </div>
          <div class="order-amount">
            <span class="amount-label">订单金额</span>
            <span class="amount-value">¥{{ selectedOrder.total_amount.toFixed(2) }}</span>
          </div>
        </div>

        <div class="detail-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="客户">{{ selectedOrder.customer_name }}</el-descriptions-item>
            <el-descriptions-item label="销售员">{{ selectedOrder.sales_person || '-' }}</el-descriptions-item>
            <el-descriptions-item label="销售平台">{{ getPlatformLabel(selectedOrder.platform) }}</el-descriptions-item>
            <el-descriptions-item label="原始订单号">{{ selectedOrder.original_order_no || '-' }}</el-descriptions-item>
            <el-descriptions-item label="交货日期">{{ formatDate(selectedOrder.delivery_date) }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDateTime(selectedOrder.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="配送地址" :span="2">{{ selectedOrder.delivery_address || '-' }}</el-descriptions-item>
            <el-descriptions-item label="备注信息" :span="2">{{ selectedOrder.remark || '无' }}</el-descriptions-item>
          </el-descriptions>

          <div class="items-section">
            <h4>销售商品明细</h4>
            <el-table :data="selectedOrder.items" style="width: 100%">
              <el-table-column prop="product_name" label="商品名称" />
              <el-table-column prop="product_sku" label="商品SKU" width="120" />
              <el-table-column prop="quantity" label="销售数量" width="100" />
              <el-table-column prop="unit_price" label="单价" width="100">
                <template #default="{ row }">
                  ¥{{ row.unit_price.toFixed(2) }}
                </template>
              </el-table-column>

              <el-table-column label="小计" width="120">
                <template #default="{ row }">
                  <span class="amount">¥{{ row.total_price.toFixed(2) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { salesApi, type SalesOrder, type SalesOrderStats } from '@/api/sales'
import { customerApi, type Customer } from '@/api/customers'
import { productApi, type Product } from '@/api/products'
import {
  Plus,
  Download,
  Document,
  Clock,
  Van,
  CircleCheck,
  Search,
  List,
  Grid,
  ArrowDown,
  Refresh,
  Delete,
  User,
  ShoppingCart
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const syncing = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const viewMode = ref<'table' | 'card'>('table')
const editingOrder = ref<SalesOrder | null>(null)
const selectedOrder = ref<SalesOrder | null>(null)
const orderFormRef = ref()

// 订单数据
const salesOrders = ref<SalesOrder[]>([])
const orderStats = ref<SalesOrderStats>({
  total_orders: 0,
  draft_orders: 0,
  submitted_orders: 0,
  approved_orders: 0,
  rejected_orders: 0,
  processing_orders: 0,
  shipped_orders: 0,
  cancelled_orders: 0,
  total_amount: 0,
  average_order_amount: 0
})

// 分页数据
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 客户和商品数据
const customers = ref<Customer[]>([])
const products = ref<Product[]>([])

// 搜索表单
const searchForm = reactive({
  order_no: '',
  customer_id: null as number | null,
  status: '',
  sales_person: '',
  platform: '',
  date_range: null as [Date, Date] | null
})

// 订单表单
const orderForm = reactive({
  customer_id: null as number | null,
  delivery_date: null as string | null,
  delivery_address: '',
  sales_person: '',
  platform: 'manual' as string,
  original_order_no: '',
  remark: '',
  items: [] as Array<{
    line_number: number
    product_id: number | null
    product_name?: string
    product_sku?: string
    unit_price: number
    quantity: number
    total_price: number
    shipped_quantity: number
  }>
})

// 表单验证规则
const orderRules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  sales_person: [
    { required: true, message: '请输入销售员', trigger: 'blur' }
  ],
  delivery_date: [
    { required: true, message: '请选择交货日期', trigger: 'change' }
  ],
  platform: [
    { required: true, message: '请选择销售平台', trigger: 'change' }
  ]
}

// 方法
const fetchSalesOrders = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 添加搜索条件
    if (searchForm.order_no) {
      params.order_no = searchForm.order_no
    }
    if (searchForm.customer_id) {
      params.customer_id = searchForm.customer_id
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.sales_person) {
      params.sales_person = searchForm.sales_person
    }
    if (searchForm.platform) {
      params.platform = searchForm.platform
    }
    if (searchForm.date_range && searchForm.date_range.length === 2) {
      params.start_date = searchForm.date_range[0].toISOString()
      params.end_date = searchForm.date_range[1].toISOString()
    }

    // 调用销售订单API
    const response = await salesApi.getSalesOrders(params)

    if (response) {
      salesOrders.value = response.items || []
      total.value = response.total || 0
    } else {
      salesOrders.value = []
      total.value = 0
    }

    // 获取统计数据
    const statsResponse = await salesApi.getSalesStats()
    if (statsResponse) {
      orderStats.value = statsResponse
    }
  } catch (error: any) {
    console.error('获取销售订单失败:', error)
    ElMessage.error('获取销售订单失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

const fetchCustomers = async () => {
  try {
    const response = await customerApi.getCustomers()
    if (response && (response as any).items) {
      customers.value = (response as any).items
    }
  } catch (error: any) {
    console.error('获取客户列表失败:', error)
  }
}

const fetchProducts = async () => {
  try {
    const response = await productApi.getProducts()
    if (response && (response as any).items) {
      products.value = (response as any).items
    }
  } catch (error: any) {
    console.error('获取商品列表失败:', error)
  }
}

const searchOrders = () => {
  currentPage.value = 1
  fetchSalesOrders()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    order_no: '',
    customer_id: null,
    status: '',
    sales_person: '',
    platform: '',
    date_range: null
  })
  currentPage.value = 1
  fetchSalesOrders()
}

// 按状态筛选订单
const filterByStatus = (status: string) => {
  // 重置搜索表单
  Object.assign(searchForm, {
    order_no: '',
    customer_id: null,
    status: status || undefined,
    sales_person: '',
    platform: undefined,
    start_date: '',
    end_date: ''
  })

  // 重置页码并搜索
  currentPage.value = 1
  fetchSalesOrders()

  // 显示提示信息
  if (status) {
    ElMessage.success(`已筛选${getStatusLabel(status)}状态的订单`)
  } else {
    ElMessage.success('已显示所有订单')
  }
}

const syncPlatformOrders = async () => {
  syncing.value = true
  try {
    ElMessage.info('开始同步电商平台订单...')
    // 这里可以调用同步API
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('平台订单同步完成')
    fetchSalesOrders()
  } catch (error) {
    ElMessage.error('同步平台订单失败')
  } finally {
    syncing.value = false
  }
}

const exportOrders = () => {
  ElMessage.info('导出功能开发中...')
}



const handleCreateOrder = () => {
  resetOrderForm()
  // 默认添加一个空的商品明细
  addOrderItem()
  showCreateDialog.value = true
}

const viewOrderDetail = (order: SalesOrder) => {
  selectedOrder.value = order
  showDetailDialog.value = true
}

// 提交订单（手工录入）
const submitOrder = async (order: SalesOrder) => {
  try {
    await salesApi.updateOrderStatus(order.id!, 'submitted')
    ElMessage.success('订单提交成功，等待审核')
    fetchSalesOrders()
  } catch (error: any) {
    ElMessage.error('提交订单失败: ' + (error.response?.data?.detail || error.message))
  }
}

// 审核订单（手工录入）
const approveOrder = async (order: SalesOrder) => {
  try {
    await ElMessageBox.confirm(
      '确认审核通过此订单吗？审核通过后订单将进入已审核状态。',
      '审核确认',
      {
        type: 'success',
        confirmButtonText: '审核通过',
        cancelButtonText: '取消'
      }
    )

    await salesApi.updateOrderStatus(order.id!, 'approved')
    ElMessage.success('订单已审核通过')
    fetchSalesOrders()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('审核订单失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

// 审核拒绝订单
const rejectOrder = async (order: SalesOrder) => {
  try {
    await ElMessageBox.confirm(
      '确认审核拒绝此订单吗？拒绝后订单将变为已拒绝状态，不可再进行操作。',
      '审核拒绝',
      {
        type: 'warning',
        confirmButtonText: '审核拒绝',
        cancelButtonText: '取消'
      }
    )

    // 统一变为已拒绝状态
    await salesApi.updateOrderStatus(order.id!, 'rejected')
    ElMessage.success('订单已审核拒绝')
    fetchSalesOrders()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('审核拒绝失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}
// 撤回订单（仅手工建单）
const withdrawOrder = async (order: SalesOrder) => {
  try {
    await ElMessageBox.confirm(
      '确认撤回此订单吗？撤回后订单将变为草稿状态，可重新编辑。',
      '撤回订单',
      {
        type: 'warning',
        confirmButtonText: '撤回',
        cancelButtonText: '取消'
      }
    )

    await salesApi.updateOrderStatus(order.id!, 'draft')
    ElMessage.success('订单已撤回，状态已变更为草稿')
    fetchSalesOrders()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('撤回订单失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}





// 发货订单
const shipOrder = async (order: SalesOrder) => {
  try {
    await ElMessageBox.confirm(
      '确认发货此订单吗？发货后订单将变为已发货状态。',
      '发货确认',
      {
        type: 'warning',
        confirmButtonText: '确认发货',
        cancelButtonText: '取消'
      }
    )

    await salesApi.updateOrderStatus(order.id!, 'shipped')
    ElMessage.success('订单已发货')
    fetchSalesOrders()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('发货失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const editOrder = (order: SalesOrder) => {
  editingOrder.value = order

  // 填充表单数据
  orderForm.customer_id = order.customer_id
  // 日期选择器使用 value-format="YYYY-MM-DD"，需要字符串格式
  orderForm.delivery_date = order.delivery_date
    ? (typeof order.delivery_date === 'string'
      ? order.delivery_date.split('T')[0]
      : new Date(order.delivery_date).toISOString().split('T')[0])
    : null
  orderForm.delivery_address = order.delivery_address ?? ''
  orderForm.sales_person = order.sales_person ?? ''
  orderForm.platform = order.platform
  orderForm.original_order_no = order.original_order_no ?? ''
  orderForm.remark = order.remark ?? ''

  // 填充订单明细
  orderForm.items = order.items?.map(item => ({
    line_number: item.line_number,
    product_id: item.product_id,
    product_name: item.product_name,
    product_sku: item.product_sku,
    unit_price: item.unit_price,
    quantity: item.quantity,
    total_price: item.total_price,
    shipped_quantity: item.shipped_quantity || 0
  })) || []

  showCreateDialog.value = true
}

const handleOrderAction = async (command: string, order: SalesOrder) => {
  try {
    switch (command) {
      case 'edit':
        editOrder(order)
        break
      case 'approve':
        await approveOrder(order)
        break
      case 'reject':
        await rejectOrder(order)
        break
      case 'cancel':
        await ElMessageBox.confirm('确定要取消这个订单吗？', '确认取消', {
          type: 'warning'
        })
        await salesApi.updateOrderStatus(order.id!, 'cancelled')
        ElMessage.success('订单已取消')
        break
      case 'delete':
        await ElMessageBox.confirm('确定要删除这个订单吗？删除后无法恢复！', '确认删除', {
          type: 'warning'
        })
        await salesApi.deleteSalesOrder(order.id!)
        ElMessage.success('订单已删除')
        break
    }
    fetchSalesOrders()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

// 订单表单相关方法
const addOrderItem = () => {
  const nextLineNumber = orderForm.items.length + 1
  orderForm.items.push({
    line_number: nextLineNumber,
    product_id: null,
    product_name: '',
    product_sku: '',
    unit_price: 0,
    quantity: 1,
    total_price: 0,
    shipped_quantity: 0
  })
}

const removeOrderItem = (index: number) => {
  orderForm.items.splice(index, 1)
  // 重新计算行号
  orderForm.items.forEach((item, idx) => {
    item.line_number = idx + 1
  })
}

const onProductChange = (row: any, index: number) => {
  const product = products.value.find(p => p.id === row.product_id)
  if (product) {
    row.product_name = product.name
    row.product_sku = product.sku
    row.unit_price = product.price
    calculateItemTotal(row, index)
  }
}

const calculateItemTotal = (row: any, index: number) => {
  row.total_price = row.unit_price * row.quantity
}

const calculateTotalAmount = () => {
  return orderForm.items.reduce((total, item) => total + item.total_price, 0)
}

const resetOrderForm = () => {
  orderForm.customer_id = null
  orderForm.delivery_date = null
  orderForm.delivery_address = ''
  orderForm.sales_person = ''
  orderForm.platform = 'manual'
  orderForm.original_order_no = ''
  orderForm.remark = ''
  orderForm.items = []
  editingOrder.value = null
}

const handleDialogClose = () => {
  resetOrderForm()
  showCreateDialog.value = false
}

const handleSaveOrder = async () => {
  if (!orderFormRef.value) return

  try {
    // 验证表单
    await orderFormRef.value.validate()

    // 验证订单明细
    if (orderForm.items.length === 0) {
      ElMessage.error('请至少添加一个商品')
      return
    }

    // 验证所有明细都已选择商品
    const hasEmptyProduct = orderForm.items.some(item => !item.product_id)
    if (hasEmptyProduct) {
      ElMessage.error('请为所有明细选择商品')
      return
    }

    saving.value = true

    // 构建提交数据
    const submitData = {
      customer_id: orderForm.customer_id!,
      total_amount: calculateTotalAmount(),
      status: editingOrder.value
        ? editingOrder.value.status
        : (orderForm.platform === 'manual' ? 'draft' : 'submitted'),
      delivery_date: orderForm.delivery_date
        ? new Date(orderForm.delivery_date).toISOString()
        : undefined,
      delivery_address: orderForm.delivery_address,
      sales_person: orderForm.sales_person,
      platform: orderForm.platform as any,
      original_order_no: orderForm.original_order_no || undefined,
      remark: orderForm.remark || undefined,
      items: orderForm.items
        .filter(item => item.product_id !== null && item.product_id !== undefined)
        .map(item => ({
          line_number: item.line_number,
          product_id: item.product_id!,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.total_price
        }))
    }

    if (editingOrder.value) {
      // 更新订单
      await salesApi.updateSalesOrder(editingOrder.value.id!, submitData)
      ElMessage.success('订单更新成功')
    } else {
      // 创建订单
      await salesApi.createSalesOrder(submitData)
      ElMessage.success('订单创建成功')
    }

    // 刷新列表
    await fetchSalesOrders()

    // 关闭对话框
    handleDialogClose()

  } catch (error: any) {
    ElMessage.error('保存失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    saving.value = false
  }
}

// 工具函数
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'info',
    submitted: 'warning',
    approved: 'primary',
    rejected: 'danger',
    shipped: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'default'
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    submitted: '已提交',
    approved: '已审核',
    rejected: '已拒绝',
    shipped: '已发货',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getPlatformTagType = (platform: string) => {
  const platformMap: Record<string, string> = {
    manual: 'info',
    taobao: 'warning',
    tmall: 'danger',
    jd: 'primary',
    pdd: 'success',
    douyin: 'info',
    xiaohongshu: 'warning',
    wechat: 'success'
  }
  return platformMap[platform] || 'default'
}

const getPlatformLabel = (platform: string) => {
  const platformMap: Record<string, string> = {
    manual: '手工录入',
    taobao: '淘宝',
    tmall: '天猫',
    jd: '京东',
    pdd: '拼多多',
    douyin: '抖音',
    xiaohongshu: '小红书',
    wechat: '微信'
  }
  return platformMap[platform] || platform
}

const formatDate = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchSalesOrders()
  fetchCustomers()
  fetchProducts()
})
</script>

<style scoped>
.sales-order-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 订单概览 */
.order-overview {
  margin-bottom: 12px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-card.clickable {
  cursor: pointer;
}

.overview-card.clickable:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.total {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.draft {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.overview-icon.submitted {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-icon.approved {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.processing {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.overview-icon.shipped {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.search-card, .table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-header, .table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.search-header h3, .table-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.search-actions, .table-actions {
  display: flex;
  gap: 12px;
}

.search-form {
  margin-top: 20px;
}

.amount {
  color: #f56c6c;
  font-weight: 600;
}

.card-view {
  margin-top: 20px;
}

.order-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.order-no {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.order-content {
  margin-bottom: 16px;
}

.order-content p {
  margin: 8px 0;
  font-size: 14px;
  color: #606266;
}

.order-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-date-editor.el-input) {
  border-radius: 6px;
}

/* 订单表单样式 */
.order-form {
  max-height: 70vh;
  overflow-y: auto;
}

.order-items {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
}

.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.total-amount {
  font-size: 16px;
  font-weight: 600;
  color: #f56c6c;
}

.empty-items {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-table .el-input-number) {
  width: 100%;
}

:deep(.el-table .el-input-number .el-input__wrapper) {
  padding: 0 8px;
}

/* 表格操作区域样式 */
.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.table-actions .el-button {
  margin: 0;
}

/* 订单详情对话框样式 */
.order-detail {
  padding: 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.order-basic-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.order-basic-info h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.order-amount {
  text-align: right;
}

.amount-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.amount-value {
  font-size: 24px;
  font-weight: 600;
  color: #f56c6c;
}

.detail-content {
  padding: 0;
}

.items-section {
  margin-top: 24px;
}

.items-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

:deep(.el-descriptions) {
  margin-bottom: 0;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 销售商品表单 */
.sales-items {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
  margin-right: 0;
  width: 100%;
}

.items-header {
  margin-bottom: 16px;
}

.total-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.total-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}

.total-label {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.total-amount {
  font-size: 20px;
  font-weight: 700;
  color: #E6A23C;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

/* 按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
</style>
