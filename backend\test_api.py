#!/usr/bin/env python3
"""
测试API接口
"""

import sys
import os
import requests
import json

def test_api():
    """测试可用销售订单API"""
    
    try:
        print("🧪 测试可用销售订单API...")
        
        # 测试API接口
        url = "http://127.0.0.1:8000/api/sales/available-orders-for-outbound/"
        
        print(f"\n📡 调用API: {url}")
        response = requests.get(url)
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API调用成功")
            print(f"   返回数据类型: {type(data)}")
            print(f"   可用订单数量: {len(data)}")
            
            # 显示前几个订单的信息
            for i, order in enumerate(data[:3]):
                print(f"\n   订单 {i+1}:")
                print(f"     ID: {order.get('id')}")
                print(f"     订单号: {order.get('order_no')}")
                print(f"     客户: {order.get('customer_name')}")
                print(f"     状态: {order.get('status')}")
                
                remaining_items = order.get('remaining_items', [])
                print(f"     剩余明细数量: {len(remaining_items)}")
                
                total_remaining = sum(item.get('quantity', 0) for item in remaining_items)
                print(f"     总剩余数量: {total_remaining}")
                
                # 显示前几个剩余明细
                for j, item in enumerate(remaining_items[:2]):
                    print(f"       明细 {j+1}: 行{item.get('line_number')} - {item.get('product_name')} (剩余{item.get('quantity')}件)")
        else:
            print(f"   ❌ API调用失败")
            print(f"   响应内容: {response.text}")
        
        print("\n✅ API测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api()
